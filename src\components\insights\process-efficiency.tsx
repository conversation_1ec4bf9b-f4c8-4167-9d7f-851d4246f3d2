'use client';

import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowDownIcon } from '@phosphor-icons/react/dist/ssr/ArrowDown';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { ArrowUpIcon } from '@phosphor-icons/react/dist/ssr/ArrowUp';
import { GitPullRequestIcon } from '@phosphor-icons/react/dist/ssr/GitPullRequest';
import type { ApexOptions } from 'apexcharts';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Chart } from '@/components/core/chart';

export function ProcessEfficiency(): React.JSX.Element {
  const t = useTranslations('insights');
  const theme = useTheme();

  // Sample data for review time
  const reviewTimeData = {
    dates: ['Sprint 1', 'Sprint 2', 'Sprint 3', 'Sprint 4', 'Sprint 5'],
    leadTime: [8.5, 7.2, 6.8, 7.5, 9.2],
    idleTime: [5.2, 4.5, 4.0, 4.8, 6.5],
  };

  // Sample data for sprint improvement
  const sprintImprovementData = {
    sprints: ['Sprint 1', 'Sprint 2', 'Sprint 3', 'Sprint 4', 'Sprint 5'],
    cycleTime: [9.5, 8.7, 7.8, 7.2, 6.5],
    leadTime: [12.3, 11.5, 10.2, 9.8, 8.5],
    deployFreq: [2, 3, 3, 4, 5],
  };

  // Chart options for review time chart
  const reviewTimeChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    colors: [theme.palette.primary.main, theme.palette.warning.main],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    legend: { show: true, position: 'top' },
    stroke: { curve: 'smooth', width: 3 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: reviewTimeData.dates,
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      labels: {
        formatter: (value) => `${value}h`,
        style: { colors: theme.palette.text.secondary },
      },
    },
    tooltip: {
      y: {
        formatter: (value) => `${value} hours`,
      },
    },
  };

  // Chart options for sprint improvement chart
  const sprintImprovementChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
      type: 'line',
      stacked: false,
    },
    colors: [theme.palette.primary.main, theme.palette.success.main, theme.palette.warning.main],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    legend: { show: true, position: 'top' },
    stroke: { curve: 'smooth', width: 3 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: sprintImprovementData.sprints,
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: [
      {
        title: {
          text: 'Time (days)',
          style: { color: theme.palette.text.secondary },
        },
        labels: {
          formatter: (value) => `${value}d`,
          style: { colors: theme.palette.text.secondary },
        },
      },
      {
        opposite: true,
        title: {
          text: 'Frequency',
          style: { color: theme.palette.text.secondary },
        },
        labels: {
          formatter: (value) => `${value}`,
          style: { colors: theme.palette.text.secondary },
        },
      },
    ],
    plotOptions: {
      bar: {
        columnWidth: '50%',
      },
    },
  };

  return (
    <Grid container spacing={3}>
      {/* Review Time Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.reviewTime')} subheader={t('metrics.reviewLeadTime')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={250}
                options={reviewTimeChartOptions}
                series={[
                  { name: 'Review Lead Time', data: reviewTimeData.leadTime },
                  { name: 'PR Idle Time', data: reviewTimeData.idleTime },
                ]}
                type='line'
                width='100%'
              />

              <Stack direction='row' spacing={2} alignItems='center'>
                <GitPullRequestIcon fontSize='var(--icon-fontSize-lg)' />
                <Stack sx={{ flex: 1 }}>
                  <Typography variant='body2'>
                    PRs spend an average of 6.5 hours idle (70% of total review time)
                  </Typography>
                  <Typography variant='caption' color='text.secondary'>
                    Trending upward in the last sprint (+35%)
                  </Typography>
                </Stack>
              </Stack>

              <Typography variant='body2' color='error'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.prSizePolicy')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* Sprint Improvement Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.sprintImprovement')} subheader={t('metrics.deltaCycleTime')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={250}
                options={{
                  ...sprintImprovementChartOptions,
                  chart: {
                    ...sprintImprovementChartOptions.chart,
                    type: 'line',
                  },
                }}
                series={
                  [
                    { name: 'Cycle Time', data: sprintImprovementData.cycleTime },
                    { name: 'Lead Time', data: sprintImprovementData.leadTime },
                    {
                      name: 'Deploy Frequency',
                      data: sprintImprovementData.deployFreq,
                      type: 'column',
                      yaxisIndex: 1,
                    },
                  ] as any
                }
                type='line'
                width='100%'
              />

              <Stack direction='row' spacing={2}>
                <Stack direction='row' spacing={1} alignItems='center'>
                  <ArrowDownIcon color={theme.palette.success.main} fontSize='var(--icon-fontSize-md)' />
                  <Typography variant='body2' color='success.main'>
                    Cycle time: -32%
                  </Typography>
                </Stack>

                <Stack direction='row' spacing={1} alignItems='center'>
                  <ArrowDownIcon color={theme.palette.success.main} fontSize='var(--icon-fontSize-md)' />
                  <Typography variant='body2' color='success.main'>
                    Lead time: -31%
                  </Typography>
                </Stack>

                <Stack direction='row' spacing={1} alignItems='center'>
                  <ArrowUpIcon color={theme.palette.success.main} fontSize='var(--icon-fontSize-md)' />
                  <Typography variant='body2' color='success.main'>
                    Deploy freq: +150%
                  </Typography>
                </Stack>
              </Stack>

              <Typography variant='body2' color='primary'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.adjustCeremonies')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}
