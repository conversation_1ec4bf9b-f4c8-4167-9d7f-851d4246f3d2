import 'server-only';

import { NextResponse } from 'next/server';

import { isValidLanguage, Language } from '@/lib/models/language';
import { isValidTheme, Theme } from '@/lib/models/theme';
import { isValidTimezone } from '@/lib/models/timezone';
import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';
import * as countries from 'i18n-iso-countries';
import { isValidPhoneNumber } from 'libphonenumber-js';

/**
 * @swagger
 * /api/user/onboarding:
 *   patch:
 *     tags:
 *       - User
 *     summary: Complete onboarding
 *     description: Complete the user onboarding process with profile information
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               displayName:
 *                 type: string
 *                 description: User's last name
 *               country:
 *                 type: string
 *                 description: User's country code (e.g., 'US', 'BR')
 *               timezone:
 *                 type: string
 *                 description: User's timezone
 *               phone:
 *                 type: string
 *                 description: User's phone number
 *               language:
 *                 type: string
 *                 enum: [en-US, pt-BR, es]
 *                 description: User's preferred language
 *               theme:
 *                 type: string
 *                 enum: [light, dark, system]
 *                 description: User's preferred theme
 *     responses:
 *       200:
 *         description: Onboarding completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
export async function PATCH(request: Request) {
  try {
    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.displayName) {
      return NextResponse.json({ error: 'Display name is required' }, { status: 400 });
    }

    // Validate country if provided
    if (body.country && !countries.getAlpha2Codes()[body.country]) {
      return NextResponse.json({ error: 'Invalid country' }, { status: 400 });
    }

    // Validate language if provided
    if (body.language && !isValidLanguage(body.language)) {
      return NextResponse.json({ error: 'Invalid language' }, { status: 400 });
    }

    // Validate theme if provided
    if (body.theme && !isValidTheme(body.theme)) {
      return NextResponse.json({ error: 'Invalid theme' }, { status: 400 });
    }

    // Validate timezone if provided
    if (body.timezone && !isValidTimezone(body.timezone)) {
      return NextResponse.json({ error: 'Invalid timezone' }, { status: 400 });
    }

    // Validate timezone if provided
    if (body.phone && !isValidPhoneNumber(body.phone, body.country)) {
      return NextResponse.json({ error: 'Invalid phone number' }, { status: 400 });
    }

    // Update the user in the database
    const user = await db.user.update({
      where: {
        id: currentUser.uid,
      },
      data: {
        displayName: body.displayName,
        timezone: body.timezone || null,
        language: body.language || Language.EN_US,
        country: body.country,
        theme: body.theme || Theme.system,
        phone: body.phone || null,
        onboarding: false, // Mark onboarding as completed
      },
    });

    // Return the updated user data
    return NextResponse.json(user);
  } catch (error) {
    console.error('Error completing onboarding:', error);
    return NextResponse.json({ error: 'Failed to complete onboarding' }, { status: 500 });
  }
}
