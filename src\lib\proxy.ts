import { NextRequest } from 'next/server';

/**
 * Resolve the actual URL of the request by considering the following headers:
 * - X-Forwarded-Proto
 * - X-Forwarded-Host
 * - X-Forwarded-Port
 *
 * This must be used when using request.url to generate redirect or returnTo urls.
 *
 * @param request The request object
 * @returns The resolved URL
 */
export function resolveURL(request: NextRequest): URL {
  const url = new URL(request.url);

  // Get forwarded protocol
  const forwardedProto = request.headers.get('X-Forwarded-Proto');
  if (forwardedProto) {
    if (forwardedProto.endsWith(':')) {
      url.protocol = forwardedProto;
    } else {
      url.protocol = forwardedProto + ':';
    }
  }

  // Determine the port based on protocol and forwarded headers.
  const forwardedPort = request.headers.get('X-Forwarded-Port');
  let port = '';

  if (forwardedProto === 'http' && forwardedPort) {
    port = `:${forwardedPort}`;
    url.port = forwardedPort;
  } else if (forwardedProto === 'https') {
    // If it's HTTPS, we generally don't need to specify the port unless it's non-standard.
    // Firebase might be adding the forwarded port incorrectly, so we omit it for HTTPS.
    port = '';
    url.port = '';
  }

  // Get forwarded host
  const forwardedHost = request.headers.get('X-Forwarded-Host');
  if (forwardedHost) {
    url.host = forwardedHost + port;
  }

  return url;
}
