import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { setCookie } from 'cookies-next';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/**
 * @swagger
 * /api/workspace/{workspaceId}/select:
 *   post:
 *     tags:
 *       - Workspace
 *     summary: Select workspace
 *     description: Select a workspace as the active workspace
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Workspace details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Workspace'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Workspace not found
 *       500:
 *         description: Server error
 */
export async function POST(request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Check if the user is a member of the workspace
    const membership = await db.workspaceMembership.findFirst({
      where: {
        userId: currentUser.uid,
        workspaceId: workspaceId,
      },
      include: {
        workspace: true,
      },
    });

    if (!membership) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    // Create the response
    const response = NextResponse.json(membership.workspace);

    // Set the workspace as active in cookies
    setCookie('selected-workspace', workspaceId, {
      req: request,
      res: response,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 30 * 24 * 60 * 60, // 30 days
    });

    return response;
  } catch (error) {
    console.error('Error selecting workspace:', error);
    return NextResponse.json({ error: 'Failed to select workspace' }, { status: 500 });
  }
}
