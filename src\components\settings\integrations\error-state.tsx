'use client';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { WarningCircleIcon } from '@phosphor-icons/react/dist/ssr/WarningCircle';
import { useTranslations } from 'next-intl';
import * as React from 'react';

interface ErrorStateProps {
  onRetryAction: () => void;
}

export function ErrorState({ onRetryAction }: ErrorStateProps): React.JSX.Element {
  const t = useTranslations('settings.integrations');

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 8,
        px: 3,
      }}
    >
      <WarningCircleIcon
        size={64}
        color='currentColor'
        style={{ color: 'var(--mui-palette-error-main)', marginBottom: 16 }}
      />
      <Stack spacing={2} sx={{ textAlign: 'center', maxWidth: 400 }}>
        <Typography variant='h6' color='text.primary'>
          {t('errorState.title' as any)}
        </Typography>
        <Typography variant='body2' color='text.secondary'>
          {t('errorState.description' as any)}
        </Typography>
        <Button variant='contained' onClick={onRetryAction} sx={{ mt: 2 }}>
          {t('errorState.retry' as any)}
        </Button>
      </Stack>
    </Box>
  );
}
