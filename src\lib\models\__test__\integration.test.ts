import { IntegrationChannel } from '@prisma/client';
import { asIntegrationChannel } from '../integration';

describe('getIntegrationChannel', () => {
  it('returns the correct IntegrationChannel when the input channel matches a key', () => {
    const channel = 'GITHUB';
    const expectedChannel = IntegrationChannel.GitHub;
    expect(asIntegrationChannel(channel)).toBe(expectedChannel);
  });

  it('returns undefined when the input channel does not match any key', () => {
    const channel = 'INVALID_CHANNEL';
    expect(asIntegrationChannel(channel)).toBeUndefined();
  });

  it('is case-insensitive when matching the input channel to keys', () => {
    const channel = 'github';
    const expectedChannel = IntegrationChannel.GitHub;
    expect(asIntegrationChannel(channel)).toBe(expectedChannel);
  });
});
