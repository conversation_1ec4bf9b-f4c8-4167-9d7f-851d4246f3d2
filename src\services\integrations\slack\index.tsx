import {
  Integration,
  IntegrationHandler,
  IntegrationInstallProps,
  IntegrationUninstallProps,
} from '@/services/integrations/integration';
import { IntegrationChannel } from '@prisma/client';

// Slack integration data
const slackIntegration: Integration = {
  id: IntegrationChannel.Slack,
  title: 'Slack',
  darkLogo: false,
  status: 'coming-soon',
  capabilities: ['teamCommunication', 'channelActivity', 'notifications', 'alertDistribution', 'collaborationInsights'],
};

// Slack integration handler
export const slackHandler: IntegrationHandler = {
  // Return the integration data
  getIntegration: () => {
    return slackIntegration;
  },

  // Handle installation (not implemented for coming soon)
  onInstallAction: async (_props: IntegrationInstallProps) => {
    console.log('Installing Slack integration');
    // Not implemented for coming soon
    return false;
  },

  // Handle uninstallation (not implemented for coming soon)
  onUninstallAction: async (_props: IntegrationUninstallProps) => {
    console.log('Uninstalling Slack integration');
    // Not implemented for coming soon
    return false;
  },

  // This integration doesn't have a custom install flow
  hasCustomInstallFlow: () => false,
};
