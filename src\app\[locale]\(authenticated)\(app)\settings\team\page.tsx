import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Typography from '@mui/material/Typography';
import { PlusIcon } from '@phosphor-icons/react/dist/ssr/Plus';
import type { Metadata } from 'next';
import * as React from 'react';

import { config } from '@/config';

export const metadata = {
  title: `Team | Settings | ${config.site.name}`,
} satisfies Metadata;

// Sample team members data
const teamMembers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/assets/avatar.png',
    role: 'Admin',
  },
  {
    id: '2',
    name: '<PERSON>e',
    email: '<EMAIL>',
    avatar: '',
    role: 'Member',
  },
  {
    id: '3',
    name: 'Jane Smith',
    email: '<EMAIL>',
    avatar: '',
    role: 'Member',
  },
];

export default async function Page(): Promise<React.JSX.Element> {
  return (
    <Stack spacing={3}>
      <Stack direction='row' spacing={3} alignItems='center' justifyContent='space-between'>
        <Typography variant='h6'>Team</Typography>
        <Button variant='contained' startIcon={<PlusIcon fontSize='var(--icon-fontSize-md)' />}>
          Invite Member
        </Button>
      </Stack>

      <Card>
        <CardHeader title='Members' />
        <Divider />
        <CardContent>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Role</TableCell>
                <TableCell align='right'>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {teamMembers.map((member) => (
                <TableRow key={member.id}>
                  <TableCell>
                    <Stack direction='row' spacing={2} alignItems='center'>
                      <Avatar src={member.avatar}>{!member.avatar && member.name.charAt(0)}</Avatar>
                      <Typography variant='body1'>{member.name}</Typography>
                    </Stack>
                  </TableCell>
                  <TableCell>{member.email}</TableCell>
                  <TableCell>{member.role}</TableCell>
                  <TableCell align='right'>
                    <Button variant='text' color='primary' size='small'>
                      Edit
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader title='Pending Invitations' />
        <Divider />
        <CardContent>
          <Typography variant='body2' color='text.secondary'>
            No pending invitations
          </Typography>
        </CardContent>
      </Card>
    </Stack>
  );
}
