import { Event } from '../types/event';

// Type to represent an event processor function
type EventProcessorFn<T extends Event> = (_event: T) => Promise<void>;

// Type for event constructor
type EventConstructor<T extends Event> = new (..._args: any[]) => T;

// Type-safe event processor registry
class EventProcessorRegistry {
  // Store processors by event constructor
  private processors = new Map<EventConstructor<any>, EventProcessorFn<any>>();

  /**
   * Register an event processor function
   * @param eventType The event constructor type
   * @param processor The processor function
   */
  register<T extends Event>(eventType: EventConstructor<T>, processor: EventProcessorFn<T>) {
    this.processors.set(eventType, processor);
    return this; // For method chaining
  }

  /**
   * Process an event
   * @param event The event to process
   */
  async process<T extends Event>(event: T): Promise<void> {
    // Find the processor for this event's constructor
    const eventConstructor = event.constructor as EventConstructor<T>;
    const processor = this.processors.get(eventConstructor);

    if (!processor) {
      throw new Error(`No processor registered for event type: ${eventConstructor.name}`);
    }

    await processor(event);
  }

  /**
   * Check if a processor exists for a given event type
   */
  hasProcessor<T extends Event>(eventType: EventConstructor<T>): boolean {
    return this.processors.has(eventType);
  }
}

export const eventProcessorRegistry = new EventProcessorRegistry();
