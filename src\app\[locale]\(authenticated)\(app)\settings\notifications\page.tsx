import type { <PERSON>ada<PERSON> } from 'next';
import * as React from 'react';

import { Notifications } from '@/components/settings/notifications';
import { config } from '@/config';

export const metadata = {
  title: `Notifications | Settings | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  // We don't need translations here as they're handled in the component
  return <Notifications />;
}
