'use client';

import { useTranslations } from 'next-intl';
import * as React from 'react';

import { DashboardBuilder } from '@/components/dashboard/dashboard-builder';
import { useWorkspace } from '@/contexts/workspace-context';

interface PageProps {
  searchParams: Promise<{
    dashboardId?: string;
  }>;
}

export default function Page({ searchParams }: PageProps) {
  const t = useTranslations('dashboard');
  const { currentWorkspace, loading } = useWorkspace();
  const { dashboardId } = React.use(searchParams);

  if (loading) {
    return (
      <div>
        <h1>{t('loading')}</h1>
      </div>
    );
  }

  if (!currentWorkspace) {
    return (
      <div>
        <h1>{t('error')}</h1>
        <p>No workspace selected. Please select a workspace first.</p>
      </div>
    );
  }

  return <DashboardBuilder dashboardId={dashboardId} workspaceId={currentWorkspace.id} isEditable={true} />;
}
