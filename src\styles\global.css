/* Remove if fonts are not used */
@import '@fontsource/inter/100.css';
@import '@fontsource/inter/200.css';
@import '@fontsource/inter/300.css';
@import '@fontsource/inter/400.css';
@import '@fontsource/inter/500.css';
@import '@fontsource/inter/600.css';
@import '@fontsource/inter/700.css';
@import '@fontsource/inter/800.css';
@import '@fontsource/inter/900.css';
@import '@fontsource/roboto-mono/300.css';
@import '@fontsource/roboto-mono/400.css';
@import '@fontsource/plus-jakarta-sans/600.css';
@import '@fontsource/plus-jakarta-sans/700.css';

/* Variables */
:root {
  --icon-fontSize-sm: 1rem;
  --icon-fontSize-md: 1.25rem;
  --icon-fontSize-lg: 1.5rem;
}

*:focus-visible {
  outline: 2px solid var(--mui-palette-primary-main);
}

html {
  height: 100%;
}

body {
  height: 100%;
}
