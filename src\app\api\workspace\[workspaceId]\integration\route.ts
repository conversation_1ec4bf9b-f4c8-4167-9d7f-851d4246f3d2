import { NextRequest, NextResponse } from 'next/server';

import { asIntegrationChannel } from '@/lib/models/integration';
import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';
import { IntegrationChannel, Permission } from '@prisma/client';

/**
 * @swagger
 * /api/workspace/{workspaceId}/integration:
 *   get:
 *     tags:
 *       - Integration
 *       - Workspace
 *     summary: Get workspace integrations
 *     description: Retrieves all integrations for a given workspace. Optionally filter by channel(s) via query string.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the workspace.
 *       - in: query
 *         name: channel
 *         required: false
 *         schema:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/IntegrationChannel'
 *         style: form
 *         explode: true
 *         description: Filter by one or more integration channels. Multiple values allowed. Must match IntegrationChannel enum.
 *     responses:
 *       200:
 *         description: A list of workspace integrations.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/WorkspaceIntegration'
 *       400:
 *         description: Bad request. Missing workspace ID or invalid channel value.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized. User is not authenticated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       403:
 *         description: Forbidden. User does not have access to the workspace.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ workspaceId: string }> }) {
  try {
    const authResult = await getAuthenticatedAppForUser();
    if (!authResult?.currentUser) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { workspaceId } = await params;

    if (!workspaceId) {
      return NextResponse.json({ message: 'Workspace ID is required' }, { status: 400 });
    }

    // Check if the user can manage integrations
    const havePermission = await db.workspaceMembership
      .findFirst({
        where: {
          userId: authResult?.currentUser.uid,
          workspaceId: workspaceId,
          role: {
            permissions: {
              some: {
                id: Permission.MANAGE_INTEGRATIONS,
              },
            },
          },
        },
      })
      .then((r) => Boolean(r));

    if (!havePermission) {
      return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
    }

    const channels = request.nextUrl.searchParams.getAll('channel');

    const integrationChannels: IntegrationChannel[] = [];
    for (const channel of channels) {
      const integrationChannel = asIntegrationChannel(channel);

      if (!integrationChannel) {
        return NextResponse.json({ message: 'Invalid channel' }, { status: 400 });
      }

      integrationChannels.push(integrationChannel);
    }

    const workspaceIntegrations = await db.workspaceIntegration.findMany({
      where: {
        workspaceId,
        channel:
          integrationChannels.length > 0
            ? {
                in: integrationChannels,
              }
            : undefined,
      },
      include: {
        github: true,
      },
    });

    return NextResponse.json(workspaceIntegrations, { status: 200 });
  } catch (error) {
    console.error('Failed to get workspace integrations:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
