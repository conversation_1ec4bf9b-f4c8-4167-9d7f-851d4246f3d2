// @ts-nocheck
import { mockIntegrationApiService, useApiServices } from '@/hooks/__mocks__/use-api-services';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';

jest.mock('@/contexts/workspace-context', () => ({
  useWorkspace: jest.fn(),
}));

// Mock the useApiServices hook
jest.mock('@/hooks/use-api-services', () => ({
  useApiServices,
}));

// Import components and hooks after mocking
import { useWorkspace } from '@/contexts/workspace-context';
import { IntegrationStatus } from '@prisma/client';
import { ActionsMenu, GitHubConfig } from '../github-config';

describe('GitHubConfig', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    useWorkspace.mockReturnValue({
      currentWorkspace: { id: 'workspace-1', name: 'Test Workspace' },
      workspaces: [{ id: 'workspace-1', name: 'Test Workspace' }],
      loading: false,
      error: null,
      selectWorkspace: jest.fn(),
      fetchWorkspaces: jest.fn(),
      fetchCurrentWorkspace: jest.fn(),
      clearError: jest.fn(),
    });

    // Default mock for GET, overridden in specific tests if needed
    mockIntegrationApiService.get.mockResolvedValue([
      {
        id: 'integration-1',
        integrationIdOnChannel: '123',
        status: 'Active',
        createdAt: new Date().toISOString(),
        github: {
          installationId: 123,
          accountLogin: 'test-org',
          accountType: 'Organization',
          accountId: '12345',
        },
      },
    ]);
    mockIntegrationApiService.setStatus.mockResolvedValue({ executedOnChannel: true, otherWorkspaces: [] });
    mockIntegrationApiService.uninstall.mockResolvedValue({ executedOnChannel: true, otherWorkspaces: [] });
  });

  it('renders loading state when isLoading is true', async () => {
    // Mock API call that sets isLoading
    mockIntegrationApiService.get.mockImplementationOnce(() => {
      return new Promise((resolve) => {
        // This promise won't resolve during the test
        setTimeout(() => resolve({}), 10000);
      });
    });

    render(
      <GitHubConfig
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Should show a loading spinner
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders with pre-loaded integration details', () => {
    const mockIntegration = {
      id: 'integration-1',
      integrationIdOnChannel: '123',
      status: 'Active' as IntegrationStatus,
      createdAt: new Date().toISOString(),
      github: {
        installationId: 123,
        accountLogin: 'test-org',
        accountType: 'Organization',
        createdAt: new Date().toISOString(),
        accountId: '12345',
      },
    };

    render(
      <GitHubConfig
        workspaceIntegrations={[mockIntegration]}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Check if the component renders the integration details
    expect(screen.getByText('github.installations')).toBeInTheDocument();

    // Check if table rows are rendered - use Material UI classes
    expect(screen.getByRole('table')).toBeInTheDocument();
    // Use getAllByRole since there might be multiple links
    const links = screen.getAllByRole('link');
    const testOrgLink = links.find((link) => link.getAttribute('href') === 'https://github.com/test-org');
    expect(testOrgLink).toBeTruthy();
    // Use getAllByText to handle multiple elements with same text
    const organizationElements = screen.getAllByText('github.organization');
    expect(organizationElements.length).toBeGreaterThan(0);
    // Status is rendered in a Chip component, so we look for it differently - use getAllByText since there might be multiple instances
    const statusElements = screen.getAllByText('github.statusActive');
    expect(statusElements.length).toBeGreaterThan(0);
  });

  it('renders empty state when there are no GitHub integrations', async () => {
    // Ensure this mock is effective for this specific test
    mockIntegrationApiService.get.mockResolvedValue([]);

    render(<GitHubConfig workspaceIntegrations={[]} integrationHandler={{ onInstallAction: jest.fn() }} />);

    // Wait for loading to complete and empty state to appear
    await waitFor(() => {
      // Check that the progressbar is gone
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();

      // Check that the empty state message is present
      const emptyStateMessage = screen.getByText((content, _) => content.includes('github.noIntegrations'));
      expect(emptyStateMessage).toBeInTheDocument();
    });

    // Verify the add button is present
    const addButton = screen.getByRole('button', { name: 'github.addInstallation' });
    expect(addButton).toBeInTheDocument();

    // Verify API was called
    expect(mockIntegrationApiService.get).toHaveBeenCalledTimes(1);
  });

  it('calls onInstallAction when Add Installation button is clicked', async () => {
    mockIntegrationApiService.get.mockResolvedValue([]);
    const mockOnInstallAction = jest.fn();

    render(
      <GitHubConfig
        workspaceIntegrations={[]}
        integrationHandler={{
          onInstallAction: mockOnInstallAction,
        }}
      />
    );

    // Wait for the empty state message to appear, implying loading is done.
    const emptyStateMessage = await screen.findByText((content, _) => content.includes('github.noIntegrations'));
    expect(emptyStateMessage).toBeInTheDocument();

    // Then, check that the progressbar is gone.
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();

    const addButton = screen.getByRole('button', { name: 'github.addInstallation' });
    fireEvent.click(addButton);

    expect(mockOnInstallAction).toHaveBeenCalledTimes(1);
    expect(mockOnInstallAction).toHaveBeenCalledWith({
      workspaceId: 'workspace-1',
      integrationApiService: expect.any(Object),
      setIsLoading: expect.any(Function),
    });
    expect(mockIntegrationApiService.get).toHaveBeenCalledTimes(1);
  });

  it('refreshes integration details when refresh button is clicked', async () => {
    // Initial integration details
    const initialData = {
      id: 'installation-1',
      integrationIdOnChannel: '1',
      createdAt: new Date().toISOString(),
      status: 'Active',
      github: {
        installationId: 1,
        accountLogin: 'test-org',
        accountType: 'Organization',
        createdAt: new Date().toISOString(),
        accountId: '12345',
      },
    };

    // Updated integration details that will be returned after refresh
    const updatedData = [
      {
        id: 'integration-1',
        integrationIdOnChannel: '1',
        createdAt: new Date().toISOString(),
        status: 'Active',
        github: {
          installationId: 1,
          accountLogin: 'test-org',
          accountType: 'Organization',
          accountId: '12345',
        },
      },
      {
        id: 'integration-2',
        integrationIdOnChannel: '2',
        createdAt: new Date().toISOString(),
        status: 'Active',
        github: {
          installationId: 2,
          accountLogin: 'new-org',
          accountType: 'Organization',
          accountId: '67890',
        },
      },
    ];

    mockIntegrationApiService.get.mockResolvedValue(updatedData);

    render(
      <GitHubConfig
        workspaceIntegrations={[initialData]}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByRole('table')).toBeInTheDocument();
    });

    // Find and click the refresh button by aria-label
    const refreshButton = screen.getByLabelText('github.refreshIntegrations');
    fireEvent.click(refreshButton);

    // Wait for the updated data to be rendered - look for the new-org item
    await waitFor(() => {
      const links = screen.getAllByRole('link');
      const newOrgLink = links.find((link) => link.getAttribute('href') === 'https://github.com/new-org');
      expect(newOrgLink).toBeTruthy();
    });

    // Check that the API was called exactly once for the refresh
    expect(mockIntegrationApiService.get).toHaveBeenCalledTimes(1);
  });

  it('shows error snackbar when API fails', async () => {
    mockIntegrationApiService.get.mockRejectedValue(new Error('API error'));

    render(
      <GitHubConfig
        workspaceIntegrations={[]}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Wait for the error snackbar to appear
    await waitFor(() => {
      expect(screen.getByText('github.errorLoadingDetails')).toBeInTheDocument();
    });
  });

  it('redirects to workspace selection when no workspace is selected', () => {
    // Mock no current workspace
    useWorkspace.mockReturnValueOnce({
      currentWorkspace: null,
      workspaces: [],
      loading: false,
      error: null,
      selectWorkspace: jest.fn(),
      fetchWorkspaces: jest.fn(),
      fetchCurrentWorkspace: jest.fn(),
      clearError: jest.fn(),
    });

    const mockRouterPush = jest.fn();
    jest.spyOn(require('@/i18n/navigation'), 'useRouter').mockReturnValueOnce({
      push: mockRouterPush,
    });

    render(
      <GitHubConfig
        workspaceIntegrations={[]}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Should redirect to workspace selection
    expect(mockRouterPush).toHaveBeenCalledWith({
      pathname: '/workspace-selection',
      query: {
        returnTo: expect.any(String),
      },
    });
  });

  it('opens confirmation dialog when action is clicked', async () => {
    // Create test data with installationId as a number to match the component behavior
    const testData = {
      id: '1',
      createdAt: new Date().toISOString(),
      integrationIdOnChannel: '1',
      status: 'Active',
      github: {
        accountLogin: 'test-org',
        accountType: 'Organization',
        accountId: '12345',
        installationId: 1,
      },
    };

    // Render the component with our test data
    render(
      <GitHubConfig
        workspaceIntegrations={[testData]}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Open the action menu by finding the button with text "github.actions"
    const actionButtons = screen.getAllByText('github.actions');
    // Find the button (not the table header)
    const actionButton = actionButtons.find((element) => element.tagName === 'BUTTON');
    expect(actionButton).toBeTruthy();
    fireEvent.click(actionButton!);

    // Wait for menu to open and click on the suspend action
    await waitFor(async () => {
      expect(screen.getByRole('menu')).toBeInTheDocument();

      // Find the suspend menu item by text
      const suspendAction = screen.getByText('github.suspendEntity');
      fireEvent.click(suspendAction);

      // Confirmation dialog should be open
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });
    });
  });

  it('calls API when action is confirmed', async () => {
    mockIntegrationApiService.setStatus.mockResolvedValue({
      executedOnChannel: true,
      otherWorkspaces: [],
    });

    // Create test data with installationId
    const testData = {
      id: '1',
      createdAt: new Date().toISOString(),
      integrationIdOnChannel: '1',
      status: 'Active',
      github: {
        accountLogin: 'test-org',
        accountType: 'Organization',
        accountId: '12345',
        installationId: 1,
      },
    };

    // Render the component
    render(
      <GitHubConfig
        workspaceIntegrations={[testData]}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Open the action menu by finding the button with text "github.actions"
    const actionButtons = screen.getAllByText('github.actions');
    // Find the button (not the table header)
    const actionButton = actionButtons.find((element) => element.tagName === 'BUTTON');
    expect(actionButton).toBeTruthy();
    fireEvent.click(actionButton!);

    // Wait for menu to open and click on the suspend action
    await waitFor(async () => {
      expect(screen.getByRole('menu')).toBeInTheDocument();

      // Find the suspend menu item by text
      const suspendAction = screen.getByText('github.suspendEntity');
      fireEvent.click(suspendAction);

      // Wait for confirmation dialog to open
      await waitFor(async () => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();

        // Fill in the account name input field first
        const accountInput = screen.getByDisplayValue('');
        fireEvent.change(accountInput, { target: { value: 'test-org' } });

        // Wait for input to be filled and button to be enabled
        await waitFor(async () => {
          const confirmButton = screen.getByText('github.action.suspendButton');
          expect(confirmButton).not.toBeDisabled();

          fireEvent.click(confirmButton);

          // Should call the API with correct parameters
          await waitFor(async () => {
            expect(mockIntegrationApiService.setStatus).toHaveBeenCalledWith(
              'workspace-1',
              '1',
              IntegrationStatus.Suspended
            );

            // Should show success snackbar
            expect(screen.getByText('github.action.executedSuccessfully')).toBeInTheDocument();
          });
        });
      });
    });
  });

  it('opens result dialog when action executed on other workspaces', async () => {
    mockIntegrationApiService.setStatus.mockResolvedValueOnce({
      executedOnChannel: false,
      otherWorkspaces: [{ id: 'workspace-2', name: 'Other Workspace' }],
    });

    const testData = {
      id: '1',
      createdAt: new Date().toISOString(),
      integrationIdOnChannel: '1',
      status: IntegrationStatus.Active,
      github: {
        accountLogin: 'test-org',
        accountType: 'Organization' as const,
        accountId: '12345',
        installationId: 1,
      },
    };

    render(
      <GitHubConfig
        workspaceIntegrations={[testData]} // Start with some data so no initial fetch for empty
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    const actionButtons = screen.getAllByText('github.actions');
    const actionButton = actionButtons.find((element) => element.tagName === 'BUTTON');
    fireEvent.click(actionButton!);

    const menu = await screen.findByRole('menu');
    const suspendAction = within(menu).getByText('github.suspendEntity');
    fireEvent.click(suspendAction);

    const confirmationDialog = await screen.findByRole('dialog');
    const accountInput = within(confirmationDialog).getByRole('textbox');
    fireEvent.change(accountInput, { target: { value: 'test-org' } });
    const confirmSuspendButton = within(confirmationDialog).getByRole('button', {
      name: 'github.action.suspendButton',
    });
    fireEvent.click(confirmSuspendButton);

    // Wait for the confirmation dialog to close and result dialog to appear
    await waitFor(() => {
      // Check if any dialog is still present and look for the result dialog specifically
      const dialogs = screen.queryAllByRole('dialog');
      expect(dialogs.length).toBeLessThanOrEqual(1);
      if (dialogs.length === 1) {
        // If there's still a dialog, it should be the result dialog with the warning message
        expect(
          within(dialogs[0]).queryByText('action.result.suspendNotExecutedOnChannel', { exact: false })
        ).toBeInTheDocument();
      }
    });

    // Now the result dialog should be present
    const resultDialog = await screen.findByRole('dialog', { name: /action.title/i });
    expect(resultDialog).toBeInTheDocument();
    expect(
      within(resultDialog).getByText('action.result.suspendNotExecutedOnChannel', { exact: false })
    ).toBeInTheDocument();
  });

  it('closes result dialog and refreshes data', async () => {
    mockIntegrationApiService.setStatus.mockResolvedValueOnce({
      executedOnChannel: false,
      otherWorkspaces: [{ id: 'workspace-2', name: 'Other Workspace' }],
    });

    const testData = {
      id: '1',
      createdAt: new Date().toISOString(),
      integrationIdOnChannel: '1',
      status: 'Active' as IntegrationStatus,
      github: {
        accountLogin: 'test-org',
        accountType: 'Organization' as const,
        accountId: '12345',
        installationId: 1,
      },
    };
    const refreshedData = [
      {
        id: 'refreshed-1',
        integrationIdOnChannel: '999',
        status: 'Active' as IntegrationStatus,
        createdAt: new Date().toISOString(),
        github: {
          installationId: 999,
          accountLogin: 'refreshed-org',
          accountType: 'Organization' as const,
          accountId: '99999',
        },
      },
    ];
    // This mock is for the refreshDetails() call
    mockIntegrationApiService.get.mockResolvedValueOnce(refreshedData);

    render(
      <GitHubConfig
        workspaceIntegrations={[testData]} // Start with data
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    const actionButtons = screen.getAllByText('github.actions');
    const actionButton = actionButtons.find((element) => element.tagName === 'BUTTON');
    fireEvent.click(actionButton!);

    await screen.findByRole('menu');
    const suspendAction = screen.getByText('github.suspendEntity');
    fireEvent.click(suspendAction);

    const confirmationDialog = await screen.findByRole('dialog', { name: /action.title/i });
    const accountInput = within(confirmationDialog).getByRole('textbox');
    fireEvent.change(accountInput, { target: { value: 'test-org' } });
    const confirmSuspendButton = within(confirmationDialog).getByRole('button', {
      name: 'github.action.suspendButton',
    });
    fireEvent.click(confirmSuspendButton);

    // Wait for the confirmation dialog to close
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    const resultDialog = await screen.findByRole('dialog', { name: /action.title/i });
    expect(resultDialog).toBeInTheDocument();
    expect(
      within(resultDialog).getByText('action.result.suspendNotExecutedOnChannel', { exact: false })
    ).toBeInTheDocument();

    const closeResultDialogButton = within(resultDialog).getByRole('button', { name: /action.result.closeButton/i });
    fireEvent.click(closeResultDialogButton);

    await waitFor(() => {
      expect(screen.queryByRole('dialog', { name: /action.title/i })).not.toBeInTheDocument();
    });

    // mockIntegrationApiService.get was called once for the refresh
    expect(mockIntegrationApiService.get).toHaveBeenCalledTimes(1);

    await waitFor(() => {
      expect(screen.getAllByText('refreshed-org')[0]).toBeInTheDocument();
    });
  });

  it('handles uninstall action', async () => {
    // Setup API mock
    mockIntegrationApiService.uninstall.mockResolvedValue({
      executedOnChannel: true,
      otherWorkspaces: [],
    });

    // Create test data with installationId
    const testData = {
      id: '1',
      status: 'Active',
      createdAt: new Date().toISOString(),
      integrationIdOnChannel: '1',
      github: {
        accountLogin: 'test-org',
        accountType: 'Organization',
        accountId: '12345',
        installationId: 1,
      },
    };

    // Render the component
    render(
      <GitHubConfig
        workspaceIntegrations={[testData]}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Open the action menu by finding the button with text "github.actions"
    const actionButtons = screen.getAllByText('github.actions');
    // Find the button (not the table header)
    const actionButton = actionButtons.find((element) => element.tagName === 'BUTTON');
    expect(actionButton).toBeTruthy();
    fireEvent.click(actionButton!);

    // Wait for menu to open and find the uninstall action by text
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument();
    });
    const uninstallAction = screen.getByText('github.uninstallEntity');
    fireEvent.click(uninstallAction);

    // Wait for dialog and click confirm button by text
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Fill in the account name input field first
    const accountInput = screen.getByDisplayValue('');
    fireEvent.change(accountInput, { target: { value: 'test-org' } });

    // Wait for input to be filled and button to be enabled
    await waitFor(() => {
      const confirmButton = screen.getByText('github.action.uninstallButton');
      expect(confirmButton).not.toBeDisabled();
    });

    const confirmButton = screen.getByText('github.action.uninstallButton');
    fireEvent.click(confirmButton);

    // Should call the API with correct parameters
    await waitFor(() => {
      expect(mockIntegrationApiService.uninstall).toHaveBeenCalledWith('workspace-1', '1');
    });
  });

  it('handles API errors during action execution', async () => {
    // Setup API mock
    mockIntegrationApiService.setStatus.mockRejectedValueOnce(new Error('API error'));

    // Create test data with installationId
    const testData = {
      id: '1',
      createdAt: new Date().toISOString(),
      status: 'Active',
      integrationIdOnChannel: '1',
      github: {
        accountLogin: 'test-org',
        accountType: 'Organization',
        accountId: '12345',
        installationId: 1,
      },
    };

    // Render the component
    render(
      <GitHubConfig
        workspaceIntegrations={[testData]}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Open the action menu by finding the button with text "github.actions"
    const actionButtons = screen.getAllByText('github.actions');
    // Find the button (not the table header)
    const actionButton = actionButtons.find((element) => element.tagName === 'BUTTON');
    expect(actionButton).toBeTruthy();
    fireEvent.click(actionButton!);

    // Wait for menu to open and find the suspend action by text
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument();
    });
    const suspendAction = screen.getByText('github.suspendEntity');
    fireEvent.click(suspendAction);

    // Wait for dialog and click confirm button by text
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Fill in the account name input field first
    const accountInput = screen.getByDisplayValue('');
    fireEvent.change(accountInput, { target: { value: 'test-org' } });

    // Wait for input to be filled and button to be enabled
    await waitFor(() => {
      const confirmButton = screen.getByText('github.action.suspendButton');
      expect(confirmButton).not.toBeDisabled();
    });

    const confirmButton = screen.getByText('github.action.suspendButton');
    fireEvent.click(confirmButton);

    // Should show error snackbar
    await waitFor(() => {
      expect(screen.getByText('github.action.executedWithError')).toBeInTheDocument();
    });
  });

  it('closes result dialog and refreshes data', async () => {
    // This is the second test with this name
    mockIntegrationApiService.setStatus.mockResolvedValueOnce({
      executedOnChannel: false,
      otherWorkspaces: [{ id: 'workspace-2', name: 'Other Workspace' }],
    });

    // Create test data with installationId
    const testData = {
      id: '1',
      status: 'Active',
      createdAt: new Date().toISOString(),
      integrationIdOnChannel: '1',
      github: {
        accountLogin: 'test-org',
        accountType: 'Organization',
        accountId: '12345',
        installationId: 1,
      },
    };

    // Render the component
    render(
      <GitHubConfig
        workspaceIntegrations={[testData]}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Open the action menu by finding the button with text "github.actions"
    const actionButtons = screen.getAllByText('github.actions');
    // Find the button (not the table header)
    const actionButton = actionButtons.find((element) => element.tagName === 'BUTTON');
    expect(actionButton).toBeTruthy();
    fireEvent.click(actionButton!);

    // Wait for menu to open and find the suspend action by text
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument();
    });
    const suspendAction = screen.getByText('github.suspendEntity');
    fireEvent.click(suspendAction);

    // Wait for dialog and click confirm button by text
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument(); // This is the confirmation dialog
    });

    // Fill in the account name input field first
    const accountInput = within(screen.getByRole('dialog', { name: /action.title/i })).getByDisplayValue('');
    fireEvent.change(accountInput, { target: { value: 'test-org' } });

    // Wait for input to be filled and button to be enabled
    await waitFor(() => {
      const confirmButton = within(screen.getByRole('dialog', { name: /action.title/i })).getByText(
        'github.action.suspendButton'
      );
      expect(confirmButton).not.toBeDisabled();
    });

    const confirmButton = within(screen.getByRole('dialog', { name: /action.title/i })).getByText(
      'github.action.suspendButton'
    );
    fireEvent.click(confirmButton);

    // Wait for the confirmation dialog to disappear
    await waitFor(() => {
      // Check if we have a result dialog (not confirmation dialog)
      const dialogs = screen.queryAllByRole('dialog');
      if (dialogs.length > 0) {
        // If there's a dialog, it should be the result dialog with the expected content
        expect(
          within(dialogs[0]).queryByText('action.result.suspendNotExecutedOnChannel', { exact: false })
        ).toBeInTheDocument();
      }
    });

    // Wait for the result dialog to appear
    const resultDialog = await screen.findByRole('dialog', { name: /action.title/i });
    expect(resultDialog).toBeInTheDocument();
    expect(
      screen.getByText(/action.result.suspendNotExecutedOnChannel|github.action.result.notExecutedOnCurrentWorkspace/)
    ).toBeInTheDocument();

    // Mock the get API call that will be triggered after closing the dialog
    mockIntegrationApiService.get.mockResolvedValueOnce([testData]);

    // Close the result dialog by clicking the close button
    const closeButton = within(resultDialog).getByText('action.result.closeButton'); // Get button within resultDialog
    fireEvent.click(closeButton);

    // Should close dialog and refresh data
    await waitFor(() => {
      // Check that the result dialog is gone by querying for its specific text or the dialog itself
      expect(screen.queryByRole('dialog', { name: /action.result.title/i })).not.toBeInTheDocument();
      expect(
        screen.queryByText(
          /action.result.suspendNotexecutedOnChannel|github.action.result.notExecutedOnCurrentWorkspace/
        )
      ).not.toBeInTheDocument();
    });

    expect(mockIntegrationApiService.get).toHaveBeenCalled();
  });

  it('handles edit installation', async () => {
    // Create test data with installationId
    const testData = {
      id: '1',
      status: 'Active',
      createdAt: new Date().toISOString(),
      integrationIdOnChannel: '1',
      github: {
        accountLogin: 'test-org',
        accountType: 'Organization',
        accountId: '12345',
        installationId: 1,
      },
    };

    // Render the component
    render(
      <GitHubConfig
        workspaceIntegrations={[testData]}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    await waitFor(async () => {
      // Open the action menu
      const actionButtons = screen.getAllByText('github.actions');
      const actionButton = actionButtons.find((element) => element.tagName === 'BUTTON');
      expect(actionButton).toBeTruthy();
      fireEvent.click(actionButton!);

      // Wait for the menu to appear
      const menu = await screen.findByRole('menu');

      expect(menu).toBeInTheDocument();

      // Use test ID to find the edit action
      const editAction = screen.getByText('github.editEntity');
      fireEvent.click(editAction);

      // Should show info snackbar
      await waitFor(() => {
        expect(screen.getByText('Edit functionality will be implemented in a future update')).toBeInTheDocument();
      });
    });
  });

  it('displays snackbar and allows it to be closed', async () => {
    // Mock API failure
    mockIntegrationApiService.get.mockRejectedValueOnce(new Error('API error'));

    render(
      <GitHubConfig
        workspaceIntegrations={null}
        integrationHandler={{
          onInstallAction: jest.fn(),
        }}
      />
    );

    // Wait for the error snackbar to appear
    await waitFor(() => {
      expect(screen.getByText('github.errorLoadingDetails')).toBeInTheDocument();
    });

    // Close the snackbar
    const alert = screen.getByRole('alert');
    const closeButton = alert.querySelector('button'); // Assuming the Alert has a close button
    if (closeButton) {
      fireEvent.click(closeButton);

      // Wait for snackbar to be closed
      await waitFor(() => {
        expect(screen.queryByText('github.errorLoadingDetails')).not.toBeInTheDocument();
      });
    }
  });

  describe('ActionsMenu', () => {
    // Test for the ActionsMenu component
    it('renders menu button in compact mode on small screens', () => {
      // Mock window.innerWidth to simulate small screen
      global.innerWidth = 500;

      // Manually trigger resize event to update the component state
      global.dispatchEvent(new Event('resize'));

      // Render the ActionsMenu component
      const { getByLabelText } = render(
        <ActionsMenu
          integrationId='integration-1'
          entityStatus='Active'
          onClickAction={jest.fn()}
          onEditAction={jest.fn()}
        />
      );

      // Check that the compact button is rendered
      const menuButton = getByLabelText('github.actions');
      expect(menuButton).toBeInTheDocument();
      expect(menuButton.tagName).toBe('BUTTON');

      // Reset window size
      global.innerWidth = 1024;
      global.dispatchEvent(new Event('resize'));
    });

    it('renders menu button in full mode on large screens', () => {
      // Mock window.innerWidth to simulate large screen
      global.innerWidth = 1024;

      // Manually trigger resize event to update the component state
      global.dispatchEvent(new Event('resize'));

      // Render the ActionsMenu component
      render(
        <ActionsMenu
          integrationId='integration-1'
          entityStatus='Active'
          onClickAction={jest.fn()}
          onEditAction={jest.fn()}
        />
      );

      // Check that the full button is rendered
      const menuButton = screen.getByText('github.actions');
      expect(menuButton).toBeInTheDocument();
      expect(menuButton.textContent).toContain('github.actions');
    });

    it('opens menu when button is clicked', () => {
      // Render the ActionsMenu component
      render(
        <ActionsMenu
          integrationId='integration-1'
          entityStatus='Active'
          onClickAction={jest.fn()}
          onEditAction={jest.fn()}
        />
      );

      // Click the button to open the menu
      const menuButton = screen.getByText('github.actions');
      fireEvent.click(menuButton);

      // Menu should be open
      expect(screen.getByRole('menu')).toBeInTheDocument();
    });

    it('calls onEdit when edit action is clicked', () => {
      const mockOnEdit = jest.fn();

      // Render the ActionsMenu component
      render(
        <ActionsMenu
          integrationId='integration-1'
          entityStatus='Active'
          onClickAction={jest.fn()}
          onEditAction={mockOnEdit}
        />
      );

      // Open the menu
      const menuButton = screen.getByText('github.actions');
      fireEvent.click(menuButton);

      // Find and click the edit action by finding menu item with pencil icon
      const menuItems = screen.getAllByRole('menuitem');
      const editAction = menuItems[0]; // First item is edit
      fireEvent.click(editAction);

      // Check if onEdit was called with correct parameter
      expect(mockOnEdit).toHaveBeenCalledWith('integration-1');
    });

    it('calls onActionClick with suspend when entity status is Active', () => {
      const mockOnActionClick = jest.fn();

      // Render the ActionsMenu component
      render(
        <ActionsMenu
          integrationId='integration-1'
          entityStatus='Active'
          onClickAction={mockOnActionClick}
          onEditAction={jest.fn()}
        />
      );

      // Open the menu
      const menuButton = screen.getByText('github.actions');
      fireEvent.click(menuButton);

      // Find and click the suspend action by looking for the text
      const suspendAction = screen.getByText('github.suspendEntity');
      fireEvent.click(suspendAction);

      // Check if onActionClick was called with correct parameters
      expect(mockOnActionClick).toHaveBeenCalledWith('integration-1', IntegrationStatus.Suspended);
    });

    it('calls onActionClick with active when entity status is Suspended', () => {
      const mockOnActionClick = jest.fn();

      // Render the ActionsMenu component
      render(
        <ActionsMenu
          integrationId='integration-1'
          entityStatus='Suspended'
          onClickAction={mockOnActionClick}
          onEditAction={jest.fn()}
        />
      );

      // Open the menu
      const menuButton = screen.getByText('github.actions');
      fireEvent.click(menuButton);

      // Find and click the resume action by looking for the text
      const resumeAction = screen.getByText('github.resumeEntity');
      fireEvent.click(resumeAction);

      // Check if onActionClick was called with correct parameters
      expect(mockOnActionClick).toHaveBeenCalledWith('integration-1', IntegrationStatus.Active);
    });

    it('calls onActionClick with uninstall for any entity status', () => {
      const mockOnActionClick = jest.fn();

      // Render the ActionsMenu component
      render(
        <ActionsMenu
          integrationId='integration-1'
          entityStatus='Active'
          onClickAction={mockOnActionClick}
          onEditAction={jest.fn()}
        />
      );

      // Open the menu
      const menuButton = screen.getByText('github.actions');
      fireEvent.click(menuButton);

      // Find and click the uninstall action by looking for the text
      const uninstallAction = screen.getByText('github.uninstallEntity');
      fireEvent.click(uninstallAction);

      // Check if onActionClick was called with correct parameters
      expect(mockOnActionClick).toHaveBeenCalledWith('integration-1', IntegrationStatus.Uninstalled);
    });
  });
});
