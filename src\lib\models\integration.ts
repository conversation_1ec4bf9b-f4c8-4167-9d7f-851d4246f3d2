import { Integration } from '@/services/integrations/integration';
import { IntegrationChannel, Prisma, WorkspaceIntegration } from '@prisma/client';

/**
 * Converts a string to an IntegrationChannel, if it matches one of the keys.
 * Case-insensitive.
 * @param channel The string to convert
 * @returns The IntegrationChannel that matches the string, or undefined if no match is found
 */
export function asIntegrationChannel(channel: string): IntegrationChannel | undefined {
  return (Object.keys(IntegrationChannel) as (keyof typeof IntegrationChannel)[]).find(
    (key) => IntegrationChannel[key].toLowerCase() === channel.toLowerCase()
  ) as IntegrationChannel;
}

// Must be updated when adding new channels
export type WorkspaceIntegrationWithChannels = Prisma.WorkspaceIntegrationGetPayload<{
  include: { github: true };
}>;

export interface CombinedIntegration extends Integration {
  installed: boolean;
  integrations?: WorkspaceIntegration[];
}
