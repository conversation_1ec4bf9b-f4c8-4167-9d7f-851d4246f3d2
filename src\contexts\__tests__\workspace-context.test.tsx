import { Workspace } from '@prisma/client';
import { act, renderHook, waitFor } from '@testing-library/react';
import { deleteCookie, setCookie } from 'cookies-next';
import React from 'react';

import { mockApiServices, mockWorkspaceApiService, useApiServices } from '@/hooks/__mocks__/use-api-services';

// Import after mock
import { useWorkspace, WorkspaceProvider } from '../workspace-context';

// Mock dependencies
jest.mock('cookies-next');

const mockSetCookie = setCookie as jest.MockedFunction<typeof setCookie>;
const mockDeleteCookie = deleteCookie as jest.MockedFunction<typeof deleteCookie>;

// Mock data
const mockWorkspace1: Workspace = {
  id: 'workspace-1',
  name: 'Test Workspace 1',
  avatar: null,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

const mockWorkspace2: Workspace = {
  id: 'workspace-2',
  name: 'Test Workspace 2',
  avatar: 'avatar-url',
  createdAt: new Date('2024-01-02'),
  updatedAt: new Date('2024-01-02'),
};

const mockWorkspaces = [mockWorkspace1, mockWorkspace2];

// Mock the useApiServices hook
jest.mock('@/hooks/use-api-services', () => ({
  useApiServices,
}));

// Helper to create wrapper
const createWrapper = () => {
  return ({ children }: { children: React.ReactNode }) => <WorkspaceProvider>{children}</WorkspaceProvider>;
};

describe('WorkspaceContext', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Set default successful responses
    mockWorkspaceApiService.getWorkspaces.mockResolvedValue(mockWorkspaces);
    mockWorkspaceApiService.getSelectedWorkspace.mockResolvedValue(mockWorkspace1);
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  describe('Provider Initialization', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should initialize with loading state', () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      expect(result.current.loading).toBe(true);
      expect(result.current.currentWorkspace).toBeNull();
      expect(result.current.workspaces).toEqual([]);
      expect(result.current.error).toBeNull();
    });

    it('should fetch workspaces and current workspace on mount', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Fast-forward the debounce timer
      act(() => {
        jest.advanceTimersByTime(100);
      });

      await waitFor(() => {
        expect(mockApiServices.workspaceApiService.getWorkspaces).toHaveBeenCalledTimes(1);
        expect(mockApiServices.workspaceApiService.getSelectedWorkspace).toHaveBeenCalledTimes(1);
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.workspaces).toEqual(mockWorkspaces);
        expect(result.current.currentWorkspace).toEqual(mockWorkspace1);
        expect(result.current.error).toBeNull();
      });

      // Verify cookie was set for current workspace
      expect(mockSetCookie).toHaveBeenCalledWith('selected-workspace', mockWorkspace1.id, {
        path: '/',
        maxAge: 30 * 24 * 60 * 60,
        secure: false, // NODE_ENV is 'test'
        sameSite: 'strict',
      });
    });

    it('should handle error when fetching workspaces fails', async () => {
      const error = new Error('selection.errors.loadFailed');
      mockWorkspaceApiService.getWorkspaces.mockRejectedValue(error);

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      act(() => {
        jest.advanceTimersByTime(100);
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toEqual(error);
        expect(result.current.workspaces).toEqual([]);
        expect(result.current.currentWorkspace).toBeNull();
      });
    });

    it('should handle no current workspace selected gracefully', async () => {
      mockWorkspaceApiService.getSelectedWorkspace.mockRejectedValue(new Error('No workspace selected'));

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      act(() => {
        jest.advanceTimersByTime(100);
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.workspaces).toEqual(mockWorkspaces);
        expect(result.current.currentWorkspace).toBeNull();
        expect(result.current.error).toBeNull(); // Should not be an error
      });

      expect(mockDeleteCookie).toHaveBeenCalledWith('selected-workspace', { path: '/' });
    });

    it('should set secure cookie in production environment', async () => {
      // Mock NODE_ENV for this test
      const originalEnv = process.env.NODE_ENV;
      process.env = { ...process.env, NODE_ENV: 'production' };

      const wrapper = createWrapper();
      renderHook(() => useWorkspace(), { wrapper });

      act(() => {
        jest.advanceTimersByTime(100);
      });

      await waitFor(() => {
        expect(mockSetCookie).toHaveBeenCalledWith('selected-workspace', mockWorkspace1.id, {
          path: '/',
          maxAge: 30 * 24 * 60 * 60,
          secure: true, // Should be true in production
          sameSite: 'strict',
        });
      });

      // Restore original NODE_ENV
      process.env = { ...process.env, NODE_ENV: originalEnv };
    });

    it('should handle initialization error with non-Error object', async () => {
      mockWorkspaceApiService.getWorkspaces.mockRejectedValue('String error');

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      act(() => {
        jest.advanceTimersByTime(100);
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toEqual(new Error('selection.errors.loadFailed'));
        expect(result.current.workspaces).toEqual([]);
        expect(result.current.currentWorkspace).toBeNull();
      });
    });

    it('should handle component unmounting during initialization', async () => {
      const wrapper = createWrapper();
      const { result, unmount } = renderHook(() => useWorkspace(), { wrapper });

      // Unmount immediately before timeout
      unmount();

      act(() => {
        jest.advanceTimersByTime(100);
      });

      // Should not cause any errors or state updates after unmount
      expect(result.current.loading).toBe(true); // Should remain in initial state
    });

    it('should prevent duplicate initialization when isInitializing is true', async () => {
      // This test is tricky because isInitializing is internal state
      // We'll test by ensuring getWorkspaces is only called once even with multiple renders
      const wrapper = createWrapper();
      const { rerender } = renderHook(() => useWorkspace(), { wrapper });

      // Force re-render during initialization
      rerender();
      rerender();

      act(() => {
        jest.advanceTimersByTime(100);
      });

      await waitFor(() => {
        expect(mockWorkspaceApiService.getWorkspaces).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('selectWorkspace', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should select workspace successfully', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Mock the select workspace response
      mockWorkspaceApiService.selectWorkspace.mockResolvedValue(mockWorkspace2);

      await act(async () => {
        await result.current.selectWorkspace(mockWorkspace2.id);
      });

      expect(mockWorkspaceApiService.selectWorkspace).toHaveBeenCalledWith(mockWorkspace2.id);
      expect(result.current.currentWorkspace).toEqual(mockWorkspace2);
      expect(result.current.error).toBeNull();

      // Verify cookie was updated
      expect(mockSetCookie).toHaveBeenCalledWith('selected-workspace', mockWorkspace2.id, {
        path: '/',
        maxAge: 30 * 24 * 60 * 60,
        secure: false,
        sameSite: 'strict',
      });
    });

    it('should handle select workspace error', async () => {
      const error = new Error('Failed to select workspace');

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Set up the error after initial load
      mockWorkspaceApiService.selectWorkspace.mockRejectedValue(error);

      // The error should be thrown and caught by the context
      await act(async () => {
        try {
          await result.current.selectWorkspace(mockWorkspace2.id);
        } catch (_) {
          // Expected to throw
        }
      });

      // Check error state after the failed operation
      expect(result.current.error).toEqual(error);
      expect(result.current.loading).toBe(false);
    });

    it('should handle non-Error objects in selectWorkspace', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Set up non-Error object rejection
      mockWorkspaceApiService.selectWorkspace.mockRejectedValue('String error');

      await act(async () => {
        try {
          await result.current.selectWorkspace(mockWorkspace2.id);
        } catch (_) {
          // Expected to throw
        }
      });

      expect(result.current.error).toEqual(new Error('Failed to select workspace'));
      expect(result.current.loading).toBe(false);
    });
  });

  describe('createWorkspace', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should create workspace successfully', async () => {
      const newWorkspaceData = { name: 'New Workspace', avatar: 'avatar-url' };
      const createdWorkspace: Workspace = {
        id: 'workspace-3',
        name: newWorkspaceData.name,
        avatar: newWorkspaceData.avatar,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Mock responses
      mockWorkspaceApiService.createWorkspace.mockResolvedValue(createdWorkspace);
      const updatedWorkspaces = [...mockWorkspaces, createdWorkspace];
      mockWorkspaceApiService.getWorkspaces.mockResolvedValue(updatedWorkspaces);

      let returnedWorkspace: Workspace;
      await act(async () => {
        returnedWorkspace = await result.current.createWorkspace(newWorkspaceData);
      });

      expect(mockWorkspaceApiService.createWorkspace).toHaveBeenCalledWith(newWorkspaceData);
      expect(mockWorkspaceApiService.getWorkspaces).toHaveBeenCalledTimes(2); // Initial + after create
      expect(result.current.workspaces).toEqual(updatedWorkspaces);
      expect(returnedWorkspace!).toEqual(createdWorkspace);
      expect(result.current.error).toBeNull();

      // Should NOT automatically select the new workspace
      expect(result.current.currentWorkspace).toEqual(mockWorkspace1);
    });

    it('should handle createWorkspace error', async () => {
      const error = new Error('Failed to create workspace');
      const newWorkspaceData = { name: 'New Workspace', avatar: 'avatar-url' };

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Mock error response
      mockWorkspaceApiService.createWorkspace.mockRejectedValue(error);

      await act(async () => {
        try {
          await result.current.createWorkspace(newWorkspaceData);
        } catch (_) {
          // Expected to throw
        }
      });

      expect(result.current.error).toEqual(error);
      expect(result.current.loading).toBe(false);
    });

    it('should handle non-Error objects in createWorkspace', async () => {
      const newWorkspaceData = { name: 'New Workspace', avatar: 'avatar-url' };

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Set up non-Error object rejection
      mockWorkspaceApiService.createWorkspace.mockRejectedValue('String error');

      await act(async () => {
        await expect(result.current.createWorkspace(newWorkspaceData)).rejects.toThrow();
      });

      expect(result.current.error).toEqual(new Error('Failed to create workspace'));
      expect(result.current.loading).toBe(false);
    });
  });

  describe('updateWorkspace', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should update workspace successfully', async () => {
      const updateData = { name: 'Updated Workspace Name' };
      const updatedWorkspace = { ...mockWorkspace1, ...updateData };

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      mockWorkspaceApiService.updateWorkspace.mockResolvedValue(updatedWorkspace);

      let returnedWorkspace: Workspace;
      await act(async () => {
        returnedWorkspace = await result.current.updateWorkspace(mockWorkspace1.id, updateData);
      });

      expect(mockWorkspaceApiService.updateWorkspace).toHaveBeenCalledWith(mockWorkspace1.id, updateData);
      expect(result.current.workspaces).toContainEqual(updatedWorkspace);
      expect(result.current.currentWorkspace).toEqual(updatedWorkspace);
      expect(returnedWorkspace!).toEqual(updatedWorkspace);
      expect(result.current.error).toBeNull();
    });

    it('should update workspace that is not currently selected', async () => {
      const updateData = { name: 'Updated Workspace Name' };
      const updatedWorkspace = { ...mockWorkspace2, ...updateData };

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      mockWorkspaceApiService.updateWorkspace.mockResolvedValue(updatedWorkspace);

      let returnedWorkspace: Workspace;
      await act(async () => {
        returnedWorkspace = await result.current.updateWorkspace(mockWorkspace2.id, updateData);
      });

      expect(mockWorkspaceApiService.updateWorkspace).toHaveBeenCalledWith(mockWorkspace2.id, updateData);
      expect(result.current.workspaces).toContainEqual(updatedWorkspace);
      expect(result.current.currentWorkspace).toEqual(mockWorkspace1); // Should remain unchanged
      expect(returnedWorkspace!).toEqual(updatedWorkspace);
      expect(result.current.error).toBeNull();
    });

    it('should handle updateWorkspace error', async () => {
      const error = new Error('Failed to update workspace');
      const updateData = { name: 'Updated Workspace Name' };

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Mock error response
      mockWorkspaceApiService.updateWorkspace.mockRejectedValue(error);

      await act(async () => {
        await expect(result.current.updateWorkspace(mockWorkspace1.id, updateData)).rejects.toThrow();
      });

      expect(result.current.error).toEqual(error);
      expect(result.current.loading).toBe(false);
    });

    it('should handle non-Error objects in updateWorkspace', async () => {
      const updateData = { name: 'Updated Workspace Name' };

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Set up non-Error object rejection
      mockWorkspaceApiService.updateWorkspace.mockRejectedValue('String error');

      await act(async () => {
        await expect(result.current.updateWorkspace(mockWorkspace1.id, updateData)).rejects.toThrow();
      });

      expect(result.current.error).toEqual(new Error('Failed to update workspace'));
      expect(result.current.loading).toBe(false);
    });
  });

  describe('deleteWorkspace', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should delete workspace successfully', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      mockWorkspaceApiService.deleteWorkspace.mockResolvedValue(undefined);

      await act(async () => {
        await result.current.deleteWorkspace(mockWorkspace2.id);
      });

      expect(mockWorkspaceApiService.deleteWorkspace).toHaveBeenCalledWith(mockWorkspace2.id);
      expect(result.current.workspaces).not.toContainEqual(mockWorkspace2);
      expect(result.current.currentWorkspace).toEqual(mockWorkspace1); // Should remain unchanged
      expect(result.current.error).toBeNull();
    });

    it('should delete currently selected workspace and clear current workspace', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Verify initial state
      expect(result.current.currentWorkspace).toEqual(mockWorkspace1);

      mockWorkspaceApiService.deleteWorkspace.mockResolvedValue(undefined);

      await act(async () => {
        await result.current.deleteWorkspace(mockWorkspace1.id);
      });

      expect(mockWorkspaceApiService.deleteWorkspace).toHaveBeenCalledWith(mockWorkspace1.id);
      expect(result.current.workspaces).not.toContainEqual(mockWorkspace1);
      expect(result.current.currentWorkspace).toBeNull(); // Should be cleared
      expect(result.current.error).toBeNull();

      // Verify cookie was deleted
      expect(mockDeleteCookie).toHaveBeenCalledWith('selected-workspace', { path: '/' });
    });

    it('should handle deleteWorkspace error', async () => {
      const error = new Error('Failed to delete workspace');

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Mock error response
      mockWorkspaceApiService.deleteWorkspace.mockRejectedValue(error);

      await act(async () => {
        await expect(result.current.deleteWorkspace(mockWorkspace2.id)).rejects.toThrow();
      });

      expect(result.current.error).toEqual(error);
      expect(result.current.loading).toBe(false);
    });

    it('should handle non-Error objects in deleteWorkspace', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Set up non-Error object rejection
      mockWorkspaceApiService.deleteWorkspace.mockRejectedValue('String error');

      await act(async () => {
        await expect(result.current.deleteWorkspace(mockWorkspace2.id)).rejects.toThrow();
      });

      expect(result.current.error).toEqual(new Error('Failed to delete workspace'));
      expect(result.current.loading).toBe(false);
    });
  });

  describe('clearWorkspaceData', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should clear all workspace data', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Verify initial state
      expect(result.current.currentWorkspace).toEqual(mockWorkspace1);
      expect(result.current.workspaces).toEqual(mockWorkspaces);

      act(() => {
        result.current.clearWorkspaceData();
      });

      expect(result.current.currentWorkspace).toBeNull();
      expect(result.current.workspaces).toEqual([]);
      expect(result.current.error).toBeNull();
      expect(mockDeleteCookie).toHaveBeenCalledWith('selected-workspace', { path: '/' });
    });
  });

  describe('fetchWorkspaces', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should fetch workspaces without force parameter', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Reset mock call count
      mockWorkspaceApiService.getWorkspaces.mockClear();

      await act(async () => {
        await result.current.fetchWorkspaces();
      });

      expect(mockWorkspaceApiService.getWorkspaces).toHaveBeenCalledTimes(1);
      expect(result.current.workspaces).toEqual(mockWorkspaces);
      expect(result.current.error).toBeNull();
    });

    it('should fetch workspaces with force parameter', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Reset mock call count
      mockWorkspaceApiService.getWorkspaces.mockClear();

      await act(async () => {
        await result.current.fetchWorkspaces(true);
      });

      expect(mockWorkspaceApiService.getWorkspaces).toHaveBeenCalledTimes(1);
      expect(result.current.workspaces).toEqual(mockWorkspaces);
      expect(result.current.error).toBeNull();
    });

    it('should handle fetch workspaces error', async () => {
      const error = new Error('selection.errors.loadFailed');
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Set up error for subsequent call
      mockWorkspaceApiService.getWorkspaces.mockRejectedValueOnce(error);

      await act(async () => {
        await result.current.fetchWorkspaces(true);
      });

      expect(result.current.error).toEqual(error);
      expect(result.current.loading).toBe(false);
    });

    it('should handle non-Error objects in fetch workspaces', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Set up non-Error object rejection
      mockWorkspaceApiService.getWorkspaces.mockRejectedValueOnce('String error');

      await act(async () => {
        await result.current.fetchWorkspaces(true);
      });

      expect(result.current.error).toEqual(new Error('selection.errors.loadFailed'));
      expect(result.current.loading).toBe(false);
    });
  });

  describe('fetchCurrentWorkspace', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should fetch current workspace successfully', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Reset mock call count
      mockWorkspaceApiService.getSelectedWorkspace.mockClear();
      mockWorkspaceApiService.getSelectedWorkspace.mockResolvedValue(mockWorkspace2);

      await act(async () => {
        await result.current.fetchCurrentWorkspace();
      });

      expect(mockWorkspaceApiService.getSelectedWorkspace).toHaveBeenCalledTimes(1);
      expect(result.current.currentWorkspace).toEqual(mockWorkspace2);
      expect(result.current.error).toBeNull();
      expect(result.current.loading).toBe(false);

      // Verify cookie was set
      expect(mockSetCookie).toHaveBeenCalledWith('selected-workspace', mockWorkspace2.id, {
        path: '/',
        maxAge: 30 * 24 * 60 * 60,
        secure: false,
        sameSite: 'strict',
      });
    });

    it('should handle null workspace in fetchCurrentWorkspace', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Reset mock call count
      mockWorkspaceApiService.getSelectedWorkspace.mockClear();
      mockWorkspaceApiService.getSelectedWorkspace.mockResolvedValue(null);

      await act(async () => {
        await result.current.fetchCurrentWorkspace();
      });

      expect(result.current.currentWorkspace).toBeNull();
      expect(result.current.error).toBeNull();
      expect(result.current.loading).toBe(false);

      // Verify cookie was deleted
      expect(mockDeleteCookie).toHaveBeenCalledWith('selected-workspace', { path: '/' });
    });

    it('should handle fetchCurrentWorkspace error', async () => {
      const error = new Error('Failed to fetch current workspace');
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      // Set up error for subsequent call
      mockWorkspaceApiService.getSelectedWorkspace.mockRejectedValueOnce(error);

      await act(async () => {
        await result.current.fetchCurrentWorkspace();
      });

      expect(result.current.currentWorkspace).toBeNull();
      expect(result.current.error).toBeNull(); // Error should not be set for fetchCurrentWorkspace
      expect(result.current.loading).toBe(false);

      // Verify cookie was deleted
      expect(mockDeleteCookie).toHaveBeenCalledWith('selected-workspace', { path: '/' });
    });
  });

  describe('clearError', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should clear error state', async () => {
      const error = new Error('Test error');
      mockWorkspaceApiService.getWorkspaces.mockRejectedValueOnce(error);

      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initial load with error
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.error).toEqual(new Error('selection.errors.loadFailed')));

      // Clear the error
      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });
  });

  describe('useWorkspace hook', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should return context value when used within provider', async () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useWorkspace(), { wrapper });

      // Wait for initialization
      act(() => {
        jest.advanceTimersByTime(100);
      });
      await waitFor(() => expect(result.current.loading).toBe(false));

      expect(result.current).toHaveProperty('currentWorkspace');
      expect(result.current).toHaveProperty('workspaces');
      expect(result.current).toHaveProperty('loading');
      expect(result.current).toHaveProperty('error');
      expect(result.current).toHaveProperty('selectWorkspace');
      expect(result.current).toHaveProperty('createWorkspace');
      expect(result.current).toHaveProperty('deleteWorkspace');
      expect(result.current).toHaveProperty('updateWorkspace');
      expect(result.current).toHaveProperty('fetchWorkspaces');
      expect(result.current).toHaveProperty('fetchCurrentWorkspace');
      expect(result.current).toHaveProperty('clearWorkspaceData');
      expect(result.current).toHaveProperty('clearError');
    });

    it('should throw error when used outside provider', () => {
      expect(() => {
        renderHook(() => useWorkspace());
      }).toThrow('useWorkspace must be used within a WorkspaceProvider');
    });
  });
});
