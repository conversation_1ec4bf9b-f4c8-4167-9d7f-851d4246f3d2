import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardHeader from '@mui/material/CardHeader';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import type { SxProps } from '@mui/material/styles';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { useFormatter, useTranslations } from 'next-intl';
import * as React from 'react';

// Status map will be updated with translations in the component
const statusMap = {
  pending: { labelKey: 'status.open', color: 'warning' },
  delivered: { labelKey: 'status.merged', color: 'success' },
  refunded: { labelKey: 'status.closed', color: 'error' },
} as const;

export interface PullRequest {
  id: string;
  customer: { name: string };
  amount: number;
  status: 'pending' | 'delivered' | 'refunded';
  createdAt: Date;
}

export interface RecentPullRequestsProps {
  orders?: PullRequest[];
  sx?: SxProps;
}

export function LatestOrders({ orders = [], sx }: RecentPullRequestsProps): React.JSX.Element {
  const t = useTranslations('git.overview');
  const format = useFormatter();

  return (
    <Card sx={sx}>
      <CardHeader title={t('titles.recentPullRequests')} />
      <Divider />
      <Box sx={{ overflowX: 'auto' }}>
        <Table sx={{ minWidth: 800 }}>
          <TableHead>
            <TableRow>
              <TableCell>{t('tableHeaders.prNumber')}</TableCell>
              <TableCell>{t('tableHeaders.author')}</TableCell>
              <TableCell sortDirection='desc'>{t('tableHeaders.date')}</TableCell>
              <TableCell>{t('tableHeaders.status')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {orders.map((order) => {
              const { labelKey, color } = statusMap[order.status] ?? {
                labelKey: 'status.unknown',
                color: 'default',
              };
              const label = t(labelKey);

              return (
                <TableRow hover key={order.id}>
                  <TableCell>{order.id}</TableCell>
                  <TableCell>{order.customer.name}</TableCell>
                  <TableCell>{format.dateTime(order.createdAt, { dateStyle: 'medium' })}</TableCell>
                  <TableCell>
                    <Chip color={color} label={label} size='small' />
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </Box>
      <Divider />
      <CardActions sx={{ justifyContent: 'flex-end' }}>
        <Button
          color='inherit'
          endIcon={<ArrowRightIcon fontSize='var(--icon-fontSize-md)' />}
          size='small'
          variant='text'
        >
          {t('actions.viewAll')}
        </Button>
      </CardActions>
    </Card>
  );
}
