import { GitRepositoryEvent, GitRepositoryEventAction } from '@/lib/events/types/git';
import { db } from '@/services/db';
import { EmitterWebhookEvent } from '@octokit/webhooks';
import { Repository, User } from '@octokit/webhooks-types';
import { GitRepository, GitRepositoryState, GitRepositoryVisibility, IntegrationChannel } from '@prisma/client';
import { logger } from '../../../logger/default-logger';
import { eventTranslatorRegistry } from '../registry';
import { convertToIntegrationProfile } from './author-converter';

/**
 * Extracts a {@link GitRepository} from a GitHub webhook event.
 * @param data Repository event data
 * @returns A partial {@link GitRepository} object
 * @throws {Error} If the repository created_at field is null
 * @throws {Error} If the repository ID is null or undefined
 * @throws {Error} If the repository name is null or undefined
 */
export function getRepositoryFromEvent(data: {
  repository: Partial<Repository>;
  now: Date;
  integrationId: string;
  workspaceId: string;
}): Partial<GitRepository> {
  let visibility: GitRepositoryVisibility = GitRepositoryVisibility.Public;
  if (data.repository.private || data.repository.visibility === 'private') {
    visibility = GitRepositoryVisibility.Private;
  } else if (data.repository.visibility === 'internal') {
    visibility = GitRepositoryVisibility.Internal;
  }

  if (!data.repository.created_at) {
    throw new Error('Repository created_at field is null');
  }

  if (!data.repository.id) {
    throw new Error('Repository ID is required');
  }

  if (!data.repository.name) {
    throw new Error('Repository name is required');
  }

  return {
    idOnChannel: data.repository.id.toString(),
    name: data.repository.name,
    defaultBranch: data.repository.default_branch,
    visibility,
    createdAt: new Date(data.repository.created_at),
    updatedAt: data.repository.updated_at ? new Date(data.repository.updated_at) : data.now,
    archivedAt: null,
    deletedAt: null,
    createdById: '',
    deletedById: '',
    archivedById: '',
    state: GitRepositoryState.Active,
    channel: IntegrationChannel.GitHub,
    integrationId: data.integrationId,
    id: '',
    workspaceId: data.workspaceId,
  };
}

/**
 * Register GitHub repository event translator
 */
eventTranslatorRegistry.registry(
  IntegrationChannel.GitHub,
  GitRepositoryEvent,
  async (event: EmitterWebhookEvent<'repository'>) => {
    const now = new Date();

    const { action, repository, sender } = event.payload;

    const installationId = event.payload.installation?.id;

    if (!installationId) {
      logger.warn('Invalid installation ID, ignoring event', { eventId: event.id });

      return null;
    }

    if (!repository.created_at) {
      logger.warn('Repository created_at field is null, ignoring event', { eventId: event.id });
      return null;
    }

    const integrations = await db.workspaceIntegration.findMany({
      where: {
        integrationIdOnChannel: installationId.toString(),
        channel: IntegrationChannel.GitHub,
      },
      include: {
        github: true,
      },
    });

    const events: GitRepositoryEvent[] = [];

    for (const integration of integrations) {
      const workspaceId = integration.workspaceId;
      const integrationId = integration.id;

      // Map GitHub repository event action to our internal event type
      let eventAction: GitRepositoryEventAction;
      switch (action) {
        case 'created':
          eventAction = GitRepositoryEventAction.Created;
          break;
        case 'deleted':
          eventAction = GitRepositoryEventAction.Deleted;
          break;
        case 'archived':
          eventAction = GitRepositoryEventAction.Archived;
          break;
        case 'unarchived':
          eventAction = GitRepositoryEventAction.Unarchived;
          break;
        default:
          // Skip other repository event types for now
          return null;
      }

      events.push(
        new GitRepositoryEvent({
          id: event.id,
          channel: IntegrationChannel.GitHub,
          integrationId: integrationId,
          workspaceId: workspaceId,
          timestamp: now,
          repository: getRepositoryFromEvent({
            repository: repository as Partial<Repository>,
            now,
            integrationId,
            workspaceId,
          }),
          repositoryEventAction: eventAction,
          author: convertToIntegrationProfile({
            user: sender as Partial<User>,
            workspaceId,
          }),
        })
      );
    }

    return events;
  }
);
