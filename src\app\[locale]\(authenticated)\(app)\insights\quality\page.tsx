import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import * as React from 'react';

import { QualityRisk } from '@/components/insights/quality-risk';
import { config } from '@/config';

export const metadata = {
  title: `Quality & Risk | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const t = await getTranslations('insights');

  return (
    <Stack spacing={3}>
      <Typography variant='h4'>{t('categories.qualityRisk')}</Typography>
      <Typography color='text.secondary' variant='body1'>
        Monitoring code quality and identifying potential risks in the development process.
      </Typography>

      <QualityRisk />
    </Stack>
  );
}
