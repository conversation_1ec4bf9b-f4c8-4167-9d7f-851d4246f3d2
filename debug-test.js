// Debug test to understand the file type validation issue

// Mock the validateFile function behavior
const validateFile = (file) => {
  const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
  if (!allowedTypes.includes(file.type)) {
    return 'invalidFileType';
  }
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    return 'File too large. Maximum size is 5MB';
  }
  return null;
};

// Test both scenarios
const textFile = { type: 'text/plain', size: 1000 };
const jpegFile = { type: 'image/jpeg', size: 1000 };
const largeFile = { type: 'image/jpeg', size: 6 * 1024 * 1024 };

console.log('Text file validation:', validateFile(textFile));
console.log('JPEG file validation:', validateFile(jpegFile));
console.log('Large file validation:', validateFile(largeFile));
