'use client';

import * as React from 'react';

import type { DashboardWidget } from '../draggable-widget';

interface Dashboard {
  id: string;
  name: string;
  description?: string;
  layout: Record<string, any>;
  isDefault: boolean;
  isPublic: boolean;
  workspaceId: string;
  createdAt: string;
  updatedAt: string;
  widgets: DashboardWidget[];
}

interface CreateWidgetData {
  type: string;
  title?: string;
  config: Record<string, any>;
  x: number;
  y: number;
  width: number;
  height: number;
}

interface UpdateWidgetPosition {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

interface CreateDashboardData {
  name: string;
  description?: string;
  workspaceId: string;
}

export function useDashboardState(dashboardId?: string, workspaceId?: string) {
  const [dashboard, setDashboard] = React.useState<Dashboard | null>(null);
  const [widgets, setWidgets] = React.useState<DashboardWidget[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  // Fetch dashboard data
  const fetchDashboard = React.useCallback(async () => {
    if (!dashboardId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/dashboard/${dashboardId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch dashboard: ${response.statusText}`);
      }

      const data = await response.json();
      setDashboard(data.dashboard);
      setWidgets(data.dashboard.widgets || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard');
    } finally {
      setIsLoading(false);
    }
  }, [dashboardId]);

  // Fetch dashboards for workspace
  const fetchWorkspaceDashboards = React.useCallback(async () => {
    if (!workspaceId || dashboardId) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/dashboard?workspaceId=${workspaceId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch dashboards: ${response.statusText}`);
      }

      const data = await response.json();
      const defaultDashboard = data.dashboards.find((d: Dashboard) => d.isDefault) || data.dashboards[0];

      if (defaultDashboard) {
        setDashboard(defaultDashboard);
        setWidgets(defaultDashboard.widgets || []);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboards');
    } finally {
      setIsLoading(false);
    }
  }, [workspaceId, dashboardId]);

  // Create dashboard
  const createDashboard = React.useCallback(async (dashboardData: CreateDashboardData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/dashboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dashboardData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create dashboard: ${response.statusText}`);
      }

      const data = await response.json();
      setDashboard(data.dashboard);
      setWidgets([]);
      return data.dashboard;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create dashboard');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Add widget
  const addWidget = React.useCallback(
    async (widgetData: CreateWidgetData) => {
      if (!dashboard) return;

      try {
        const response = await fetch(`/api/dashboard/${dashboard.id}/widgets`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(widgetData),
        });

        if (!response.ok) {
          throw new Error(`Failed to add widget: ${response.statusText}`);
        }

        const data = await response.json();
        setWidgets((prev) => [...prev, data.widget]);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to add widget');
        throw err;
      }
    },
    [dashboard]
  );

  // Update widget positions (optimistic update)
  const updateWidgetPositions = React.useCallback(
    async (updatedPositions: UpdateWidgetPosition[]) => {
      if (!dashboard) return;

      // Optimistic update
      setWidgets((prev) =>
        prev.map((widget) => {
          const update = updatedPositions.find((pos) => pos.id === widget.id);
          return update ? { ...widget, ...update } : widget;
        })
      );

      try {
        const response = await fetch(`/api/dashboard/${dashboard.id}/widgets`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ widgets: updatedPositions }),
        });

        if (!response.ok) {
          // Revert optimistic update on error
          await fetchDashboard();
          throw new Error(`Failed to update widget positions: ${response.statusText}`);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update widget positions');
        throw err;
      }
    },
    [dashboard, fetchDashboard]
  );

  // Remove widget
  const removeWidget = React.useCallback(
    async (widgetId: string) => {
      if (!dashboard) return;

      // Optimistic update
      setWidgets((prev) => prev.filter((widget) => widget.id !== widgetId));

      try {
        const response = await fetch(`/api/dashboard/${dashboard.id}/widgets/${widgetId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          // Revert optimistic update on error
          await fetchDashboard();
          throw new Error(`Failed to remove widget: ${response.statusText}`);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to remove widget');
        throw err;
      }
    },
    [dashboard, fetchDashboard]
  );

  // Initial data fetch
  React.useEffect(() => {
    if (dashboardId) {
      fetchDashboard();
    } else if (workspaceId) {
      fetchWorkspaceDashboards();
    }
  }, [dashboardId, workspaceId, fetchDashboard, fetchWorkspaceDashboards]);

  return {
    dashboard,
    widgets,
    isLoading,
    error,
    createDashboard,
    addWidget,
    updateWidgetPositions,
    removeWidget,
    refetch: dashboardId ? fetchDashboard : fetchWorkspaceDashboards,
  };
}
