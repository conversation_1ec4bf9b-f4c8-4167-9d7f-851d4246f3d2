// We need to import axios for the mocking to work

import { CoreApiService, TokenProvider } from '../core';

// Create mock functions
const mockGet = jest.fn().mockResolvedValue({ data: {} });
const mockPost = jest.fn().mockResolvedValue({ data: {} });
const mockPut = jest.fn().mockResolvedValue({ data: {} });
const mockPatch = jest.fn().mockResolvedValue({ data: {} });
const mockDelete = jest.fn().mockResolvedValue({ data: {} });
const mockRequest = jest.fn().mockResolvedValue({ data: {} });

// Mock the axios module
jest.mock('axios', () => {
  return {
    create: jest.fn(() => ({
      get: mockGet,
      post: mockPost,
      put: mockPut,
      patch: mockPatch,
      delete: mockDelete,
      request: mockRequest,
      interceptors: {
        request: { use: jest.fn(), eject: jest.fn() },
        response: { use: jest.fn(), eject: jest.fn() },
      },
    })),
  };
});

// Create a mock token provider
class MockTokenProvider implements TokenProvider {
  async getToken(): Promise<string | undefined> {
    return 'mock-token';
  }
}

describe('CoreApiService', () => {
  let apiService: CoreApiService;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create a new instance of CoreApiService with the mock token provider
    apiService = new CoreApiService(new MockTokenProvider(), undefined, 'https://api.example.com');
  });

  it('should be defined', () => {
    expect(apiService).toBeDefined();
  });

  it('should make a GET request', async () => {
    // Set up the mock response
    mockGet.mockResolvedValueOnce({ data: { success: true } });

    // Call the get method
    const result = await apiService.get('/test');

    // Verify that the mock get function was called with the correct URL
    expect(mockGet).toHaveBeenCalledWith('/test', { params: undefined });

    // Verify that the result is correct
    expect(result).toEqual({ success: true });
  });

  it('should make a POST request', async () => {
    // Set up the mock response
    mockPost.mockResolvedValueOnce({ data: { success: true } });

    // Call the post method
    const result = await apiService.post('/test', { data: 'test' });

    // Verify that the mock post function was called with the correct URL and data
    expect(mockPost).toHaveBeenCalledWith('/test', { data: 'test' });

    // Verify that the result is correct
    expect(result).toEqual({ success: true });
  });
});
