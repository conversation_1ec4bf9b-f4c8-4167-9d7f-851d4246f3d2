import { GitPullRequestEvent, GitPullRequestEventAction } from '@/lib/events/types/git';
import { db } from '@/services/db';
import { EmitterWebhookEvent } from '@octokit/webhooks';
import { Repository, User } from '@octokit/webhooks-types';
import { GitPullRequestState, IntegrationChannel } from '@prisma/client';
import { logger } from '../../../logger/default-logger';
import { eventTranslatorRegistry } from '../registry';
import { convertToIntegrationProfile } from './author-converter';
import { getRepositoryFromEvent } from './repository-translator';

/**
 * Register GitHub pull request event translator
 */
eventTranslatorRegistry.registry(
  IntegrationChannel.GitHub,
  GitPullRequestEvent,
  async (event: EmitterWebhookEvent<'pull_request'>) => {
    const now = new Date();

    const { action, pull_request, repository, sender } = event.payload;

    const installationId = (event.payload as any).installation?.id;

    if (!installationId) {
      logger.warn('Invalid installation ID, ignoring event', { eventId: event.id });
      return null;
    }

    const integrations = await db.workspaceIntegration.findMany({
      where: {
        integrationIdOnChannel: installationId.toString(),
        channel: IntegrationChannel.GitHub,
      },
      include: {
        github: true,
      },
    });

    const events: GitPullRequestEvent[] = [];

    for (const integration of integrations) {
      const workspaceId = integration.workspaceId;
      const integrationId = integration.id;

      // Map GitHub pull request event action to our internal event type
      let eventAction: GitPullRequestEventAction;
      switch (action) {
        case 'opened':
          eventAction = GitPullRequestEventAction.Opened;
          break;
        case 'closed':
          // Check if the PR was merged or just closed
          eventAction = pull_request.merged ? GitPullRequestEventAction.Merged : GitPullRequestEventAction.Closed;
          break;
        case 'reopened':
          eventAction = GitPullRequestEventAction.Reopened;
          break;
        case 'edited':
          eventAction = GitPullRequestEventAction.Edited;
          break;
        default:
          // Skip other pull request event types for now
          return null;
      }

      let state: GitPullRequestState = GitPullRequestState.OPEN;
      if (pull_request.merged) {
        state = GitPullRequestState.MERGED;
      } else if (pull_request.state === 'closed') {
        state = GitPullRequestState.CLOSED;
      }

      events.push(
        new GitPullRequestEvent({
          id: event.id,
          channel: IntegrationChannel.GitHub,
          integrationId: integrationId,
          workspaceId: workspaceId,
          timestamp: now,
          repository: getRepositoryFromEvent({
            repository: repository as Partial<Repository>,
            now,
            integrationId,
            workspaceId,
          }),
          pullRequest: {
            idOnChannel: pull_request.id.toString(),
            title: pull_request.title,
            description: pull_request.body || undefined,
            baseRef: pull_request.base.ref,
            headRef: pull_request.head.ref,
            baseSha: pull_request.base.sha,
            headSha: pull_request.head.sha,
            state,
            url: pull_request.html_url || undefined,
            number: pull_request.number,
            createdAt: new Date(pull_request.created_at),
            mergedAt: pull_request.merged_at ? new Date(pull_request.merged_at) : undefined,
            closedAt: pull_request.closed_at ? new Date(pull_request.closed_at) : undefined,
            updatedAt: now,
            isDraft: pull_request.draft || false,
            addedLines: pull_request.additions || 0,
            deletedLines: pull_request.deletions || 0,
          },
          pullRequestEventAction: eventAction,
          author: convertToIntegrationProfile({
            user: sender as Partial<User>,
            workspaceId,
          }),
          pullRequestUser: convertToIntegrationProfile({
            user: pull_request.user as Partial<User>,
            workspaceId,
          }),
          rawPayload: event,
        })
      );
    }

    return events;
  }
);
