'use client';

import { getApiService } from '@/services/api';
import { createIntegrationApiService } from '@/services/api/integration';
import { createUserApiService } from '@/services/api/user';
import { createWorkspaceApiService } from '@/services/api/workspace';
import { useMemo } from 'react';

/**
 * Custom hook that provides access to all API services.
 * This centralizes API service creation and makes it easier to mock for testing.
 */
export function useApiServices() {
  // Create the base API service
  const apiService = useMemo(() => getApiService(), []);

  // Create specific API services
  const userApiService = useMemo(() => createUserApiService(apiService), [apiService]);
  const workspaceApiService = useMemo(() => createWorkspaceApiService(apiService), [apiService]);
  const integrationApiService = useMemo(() => createIntegrationApiService(apiService), [apiService]);

  // Return all services
  return {
    apiService,
    userApiService,
    workspaceApiService,
    integrationApiService,
  };
}
