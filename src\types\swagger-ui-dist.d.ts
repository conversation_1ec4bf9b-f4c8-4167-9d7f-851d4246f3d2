// @types/swagger-ui-dist is outdated and not compatible with swagger-ui-dist v5.21.0
// creating a custom type declaration file to fix the issue
declare module 'swagger-ui-dist' {
  export interface SwaggerUIBundleOptions {
    /**
     * The DOM element where Swagger UI will be rendered
     */
    domNode?: HTMLElement;

    /**
     * The OpenAPI/Swagger specification to display
     */
    spec?: Record<string, any>;

    /**
     * URL to fetch the OpenAPI/Swagger specification from
     */
    url?: string;

    /**
     * Additional configuration options
     */
    [key: string]: any;
  }

  /**
   * Function to initialize and render Swagger UI
   */
  export function SwaggerUIBundle(_: SwaggerUIBundleOptions): any;

  /**
   * Standalone preset for Swagger UI
   */
  export const SwaggerUIStandalonePreset: any;
}
