import 'server-only';

import admin from 'firebase-admin';

function getFirebaseAdminConfig() {
  if (
    typeof process === 'undefined' ||
    process.env.FIREBASE_WEBAPP_CONFIG ||
    !process.env.NEXT_PUBLIC_FIREBASE_API_KEY
  ) {
    return undefined;
  }

  return {
    credential: admin.credential.cert({
      privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY,
      clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
      projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
    }),
    storageBucket: process.env.FIREBASE_ADMIN_STORAGE_BUCKET,
    projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  };
}

const firebaseAdminConfig = getFirebaseAdminConfig();

// Initialize Firebase Admin SDK
let adminApp: admin.app.App;

try {
  // Check if app is already initialized
  adminApp = admin.app();
} catch (_error) {
  adminApp = firebaseAdminConfig ? admin.initializeApp(firebaseAdminConfig) : admin.initializeApp();
}

export const adminAuth = admin.auth(adminApp);
export const adminStorage = admin.storage(adminApp);
export const adminFirestore = admin.firestore(adminApp);
