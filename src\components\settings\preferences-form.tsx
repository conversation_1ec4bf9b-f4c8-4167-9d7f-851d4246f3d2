'use client';

import { useRouter } from '@/i18n/navigation';
import { routing } from '@/i18n/routing';
import { useColorScheme } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import Stack from '@mui/material/Stack';
import { useTheme as useMuiTheme } from '@mui/material/styles';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { CaretDownIcon } from '@phosphor-icons/react/dist/ssr/CaretDown';
import { DesktopIcon } from '@phosphor-icons/react/dist/ssr/Desktop';
import { MoonIcon } from '@phosphor-icons/react/dist/ssr/Moon';
import { PaletteIcon } from '@phosphor-icons/react/dist/ssr/Palette';
import { SunIcon } from '@phosphor-icons/react/dist/ssr/Sun';
import { TranslateIcon } from '@phosphor-icons/react/dist/ssr/Translate';
import { Locale, useLocale, useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import * as React from 'react';

import { useCurrentUser } from '@/contexts/user-context';
import { useApiServices } from '@/hooks/use-api-services';

export function PreferencesForm(): React.JSX.Element {
  const t = useTranslations('settings.preferences');
  const router = useRouter();
  const params = useParams();
  const locale = useLocale();
  const muiTheme = useMuiTheme();
  const { updateUser } = useCurrentUser();
  const { userApiService } = useApiServices();
  const [savingLanguage, setSavingLanguage] = React.useState(false);
  const [savingTheme, setSavingTheme] = React.useState(false);

  const handleLanguageChange = async (event: React.ChangeEvent<{ value: unknown }>) => {
    const nextLocale = event.target.value as Locale;

    if (nextLocale === locale) {
      return;
    }

    setSavingLanguage(true);
    try {
      // Save to backend first
      const updatedUser = await userApiService.updateCurrentUser({
        language: nextLocale,
      });
      updateUser(updatedUser);

      // Then navigate to the new locale
      router.replace(
        // @ts-expect-error -- TypeScript will validate that only known `params`
        // are used in combination with a given `pathname`. Since the two will
        // always match for the current route, we can skip runtime checks.
        { pathname: window.location.pathname, params },
        { locale: nextLocale }
      );
      router.refresh();
    } catch (error) {
      console.error('Failed to update language preference:', error);
      // TODO: Show error toast/alert
    } finally {
      setSavingLanguage(false);
    }
  };
  const { mode, setMode } = useColorScheme();

  const handleThemeChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const newTheme = event.target.value as 'light' | 'dark' | 'system';

    setSavingTheme(true);
    try {
      // Save to backend first
      const updatedUser = await userApiService.updateCurrentUser({
        theme: newTheme,
      });
      updateUser(updatedUser);

      // Then apply the theme change
      setMode(newTheme);
    } catch (error) {
      console.error('Failed to update theme preference:', error);
      // TODO: Show error toast/alert
    } finally {
      setSavingTheme(false);
    }
  };

  // Map of locale to flag emoji
  const localeToSymbol: Record<string, string> = {
    'en-US': '🇺🇸',
    'pt-BR': '🇧🇷',
    es: '🇪🇸',
  };

  // Get language name from translation
  const getLanguageName = (localeStr: string): string => {
    return t('languageSwitch', { locale: localeStr.replaceAll('-', '_') });
  };

  return (
    <Grid container spacing={4}>
      {/* Language Selector */}
      <Grid size={{ xs: 12, md: 6 }}>
        <Stack spacing={2}>
          <Stack direction='row' spacing={1} alignItems='center'>
            <TranslateIcon fontSize='var(--icon-fontSize-md)' />
            <Typography variant='h6'>{t('language')}</Typography>
          </Stack>{' '}
          <Autocomplete
            id='language-select'
            options={routing.locales}
            value={locale}
            size='small'
            sx={{ maxWidth: 240 }}
            disabled={savingLanguage}
            loading={savingLanguage}
            onChange={(_, newValue) => {
              if (newValue) {
                const fakeEvent = { target: { value: newValue } } as any;
                handleLanguageChange(fakeEvent);
              }
            }}
            getOptionLabel={(option) => {
              if (typeof option === 'string') {
                return getLanguageName(option);
              }
              return '';
            }}
            renderOption={(props, option) => {
              const { key, ...otherProps } = props;
              return (
                <ListItem key={key} {...otherProps} dense>
                  <Box
                    component='span'
                    sx={{
                      minWidth: 36,
                      display: 'flex',
                      alignItems: 'center',
                      fontSize: '1.2rem',
                    }}
                  >
                    {localeToSymbol[option]}
                  </Box>
                  <ListItemText primary={<Typography variant='body2'>{getLanguageName(option)}</Typography>} />
                </ListItem>
              );
            }}
            renderInput={(renderParams) => {
              // Add the flag emoji as a startAdornment
              const textFieldParams = {
                ...renderParams,
                InputProps: {
                  ...renderParams.InputProps,
                  startAdornment: (
                    <Box
                      component='span'
                      sx={{
                        fontSize: '1.2rem',
                        marginRight: '8px',
                      }}
                    >
                      {localeToSymbol[locale]}
                    </Box>
                  ),
                  endAdornment: (
                    <React.Fragment>
                      {savingLanguage ? <CircularProgress color='inherit' size={20} /> : null}
                      {renderParams.InputProps.endAdornment}
                    </React.Fragment>
                  ),
                },
              };

              return (
                <TextField {...textFieldParams} variant='outlined' fullWidth placeholder={getLanguageName(locale)} />
              );
            }}
            popupIcon={<CaretDownIcon fontSize='var(--icon-fontSize-sm)' />}
            slotProps={{
              popper: {
                modifiers: [
                  {
                    name: 'offset',
                    options: {
                      offset: [0, 8],
                    },
                  },
                ],
              },
            }}
            disableClearable
            openOnFocus
          />
        </Stack>
      </Grid>

      {/* Theme Selector */}
      <Grid size={{ xs: 12, md: 6 }}>
        <Stack spacing={2}>
          <Stack direction='row' spacing={1} alignItems='center'>
            <PaletteIcon fontSize='var(--icon-fontSize-md)' />
            <Typography variant='h6'>{t('theme')}</Typography>
          </Stack>{' '}
          <FormControl component='fieldset' fullWidth>
            <RadioGroup
              aria-labelledby='theme-selector-label'
              name='theme-selector'
              value={mode}
              onChange={handleThemeChange}
            >
              <FormControlLabel
                value='system'
                control={<Radio disabled={savingTheme} />}
                label={
                  <Stack direction='row' spacing={1} alignItems='center'>
                    <DesktopIcon fontSize='var(--icon-fontSize-md)' color={muiTheme.palette.primary.main} />
                    <Typography>{t('themeOptions.system')}</Typography>
                    {savingTheme && mode === 'system' && <CircularProgress size={16} />}
                  </Stack>
                }
              />
              <FormControlLabel
                value='light'
                control={<Radio disabled={savingTheme} />}
                label={
                  <Stack direction='row' spacing={1} alignItems='center'>
                    <SunIcon fontSize='var(--icon-fontSize-md)' color={muiTheme.palette.primary.main} />
                    <Typography>{t('themeOptions.light')}</Typography>
                    {savingTheme && mode === 'light' && <CircularProgress size={16} />}
                  </Stack>
                }
              />
              <FormControlLabel
                value='dark'
                control={<Radio disabled={savingTheme} />}
                label={
                  <Stack direction='row' spacing={1} alignItems='center'>
                    <MoonIcon fontSize='var(--icon-fontSize-md)' color={muiTheme.palette.primary.main} />
                    <Typography>{t('themeOptions.dark')}</Typography>
                    {savingTheme && mode === 'dark' && <CircularProgress size={16} />}
                  </Stack>
                }
              />
            </RadioGroup>
          </FormControl>
        </Stack>
      </Grid>
    </Grid>
  );
}
