/**
 * @swagger
 * /api/dashboard/{dashboardId}/widgets:
 *   get:
 *     summary: Get all widgets for a dashboard
 *     tags: [Dashboard, Widgets]
 *     parameters:
 *       - in: path
 *         name: dashboardId
 *         required: true
 *         schema:
 *           type: string
 *         description: The dashboard ID
 *     responses:
 *       200:
 *         description: List of widgets
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Dashboard not found or access denied
 *   post:
 *     summary: Create a new widget for a dashboard
 *     tags: [Dashboard, Widgets]
 *     parameters:
 *       - in: path
 *         name: dashboardId
 *         required: true
 *         schema:
 *           type: string
 *         description: The dashboard ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [METRIC_CARD, SPARKLINE_CHART, HEATMAP, TEXT_INSIGHT, COMMITS_CHART, PULL_REQUESTS_CHART, VELOCITY_CHART, COPILOT_METRICS]
 *               title:
 *                 type: string
 *               config:
 *                 type: object
 *               x:
 *                 type: integer
 *               y:
 *                 type: integer
 *               width:
 *                 type: integer
 *               height:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Widget created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Dashboard not found or access denied
 *   put:
 *     summary: Update widget positions (bulk update for drag-and-drop)
 *     tags: [Dashboard, Widgets]
 *     parameters:
 *       - in: path
 *         name: dashboardId
 *         required: true
 *         schema:
 *           type: string
 *         description: The dashboard ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               widgets:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     x:
 *                       type: integer
 *                     y:
 *                       type: integer
 *                     width:
 *                       type: integer
 *                     height:
 *                       type: integer
 *     responses:
 *       200:
 *         description: Widget positions updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Dashboard not found or access denied
 */
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

// Schema for creating a new widget
const createWidgetSchema = z.object({
  type: z.enum([
    'METRIC_CARD',
    'SPARKLINE_CHART',
    'HEATMAP',
    'TEXT_INSIGHT',
    'COMMITS_CHART',
    'PULL_REQUESTS_CHART',
    'VELOCITY_CHART',
    'COPILOT_METRICS',
  ]),
  title: z.string().optional(),
  config: z.record(z.any()).default({}),
  x: z.number().int().min(0),
  y: z.number().int().min(0),
  width: z.number().int().min(1),
  height: z.number().int().min(1),
});

// Schema for updating widget positions (bulk update)
const updateWidgetPositionsSchema = z.object({
  widgets: z.array(
    z.object({
      id: z.string().uuid(),
      x: z.number().int().min(0),
      y: z.number().int().min(0),
      width: z.number().int().min(1),
      height: z.number().int().min(1),
    })
  ),
});

interface RouteParams {
  params: Promise<{
    dashboardId: string;
  }>;
}

/**
 * GET /api/dashboard/[dashboardId]/widgets
 * Get all widgets for a dashboard
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const result = await getAuthenticatedAppForUser();
    if (!result?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { dashboardId } = await params;

    // Verify dashboard exists and user has access
    const dashboard = await db.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspace: {
          members: {
            some: {
              user: {
                email: result.currentUser.email,
              },
            },
          },
        },
      },
    });

    if (!dashboard) {
      return NextResponse.json({ error: 'Dashboard not found or access denied' }, { status: 404 });
    }

    // Get widgets for the dashboard
    const widgets = await db.dashboardWidget.findMany({
      where: {
        dashboardId,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    return NextResponse.json({ widgets });
  } catch (error) {
    console.error('Error fetching widgets:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/dashboard/[dashboardId]/widgets
 * Create a new widget for a dashboard
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const result = await getAuthenticatedAppForUser();
    if (!result?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { dashboardId } = await params;
    const body = await request.json();
    const validatedData = createWidgetSchema.parse(body);

    // Verify dashboard exists and user has access
    const dashboard = await db.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspace: {
          members: {
            some: {
              user: {
                email: result.currentUser.email,
              },
            },
          },
        },
      },
    });

    if (!dashboard) {
      return NextResponse.json({ error: 'Dashboard not found or access denied' }, { status: 404 });
    }

    // Create the widget
    const widget = await db.dashboardWidget.create({
      data: {
        ...validatedData,
        dashboardId,
      },
    });

    return NextResponse.json({ widget }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }

    console.error('Error creating widget:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PUT /api/dashboard/[dashboardId]/widgets
 * Update widget positions (bulk update for drag-and-drop)
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const result = await getAuthenticatedAppForUser();
    if (!result?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { dashboardId } = await params;
    const body = await request.json();
    const validatedData = updateWidgetPositionsSchema.parse(body);

    // Verify dashboard exists and user has access
    const dashboard = await db.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspace: {
          members: {
            some: {
              user: {
                email: result.currentUser.email,
              },
            },
          },
        },
      },
    });

    if (!dashboard) {
      return NextResponse.json({ error: 'Dashboard not found or access denied' }, { status: 404 });
    }

    // Update widget positions in a transaction
    const updatedWidgets = await db.$transaction(
      validatedData.widgets.map((widget) =>
        db.dashboardWidget.update({
          where: {
            id: widget.id,
            dashboardId, // Ensure widget belongs to this dashboard
          },
          data: {
            x: widget.x,
            y: widget.y,
            width: widget.width,
            height: widget.height,
          },
        })
      )
    );

    return NextResponse.json({ widgets: updatedWidgets });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }

    console.error('Error updating widget positions:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
