import '@/lib/events/processors/git/git-repository-processor';
import { eventProcessorRegistry } from '@/lib/events/processors/registry';
import { GitRepositoryEvent, GitRepositoryEventAction } from '@/lib/events/types/git';
import { logger } from '@/lib/logger/default-logger';
import { db } from '@/services/db';
import { GitRepository, GitRepositoryState, GitRepositoryVisibility, IntegrationChannel } from '@prisma/client';

// Mock the db module
jest.mock('@/services/db', () => ({
  db: {
    gitRepository: {
      findFirst: jest.fn(),
      upsert: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    profile: {
      findFirst: jest.fn(),
      create: jest.fn(),
    },
  },
}));

// Mock the logger
jest.mock('@/lib/logger/default-logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock createIntegrationProfile
jest.mock('@/lib/profile/integration-profile-creator', () => ({
  createIntegrationProfile: jest.fn().mockResolvedValue({ id: 'mock-profile-id' }),
}));

describe('GitRepositoryProcessor', () => {
  const mockRepoProps: GitRepository = {
    id: 'repo-id',
    name: 'test-repo',
    idOnChannel: 'github-repo-123',
    defaultBranch: 'main',
    visibility: GitRepositoryVisibility.Public,
    createdAt: new Date(),
    updatedAt: new Date(),
    archivedAt: null,
    deletedAt: null,
    createdById: null,
    deletedById: null,
    archivedById: null,
    workspaceId: 'workspace-1',
    integrationId: 'integration-1',
    state: GitRepositoryState.Active,
    channel: IntegrationChannel.GitHub,
  };

  const mockAuthor = {
    id: 'author-id',
    name: 'Test Author',
    email: '<EMAIL>',
    idOnChannel: 'author-github-id',
    username: 'testauthor',
    workspaceId: 'workspace-1',
    channel: IntegrationChannel.GitHub,
  };

  const baseEventProps = {
    id: 'test-event-id',
    timestamp: new Date(),
    workspaceId: 'workspace-1',
    integrationId: 'integration-1',
    repository: mockRepoProps,
    author: mockAuthor,
    repositoryEventAction: GitRepositoryEventAction.Created,
  };

  const mockRepositoryEvent = new GitRepositoryEvent(baseEventProps);

  const mockExistingRepository = {
    ...mockRepoProps,
    id: 'existing-repo-id',
    state: GitRepositoryState.Active,
    workspaceId: 'workspace-1',
    integrationId: 'integration-1',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset createIntegrationProfile mock for tests that modify it
    const { createIntegrationProfile } = require('@/lib/profile/integration-profile-creator');
    (createIntegrationProfile as jest.Mock).mockResolvedValue({ id: 'mock-profile-id' });
  });

  it('should process repository created event and create repository', async () => {
    (db.gitRepository.upsert as jest.Mock).mockResolvedValue(mockExistingRepository);
    await eventProcessorRegistry.process(mockRepositoryEvent);
    expect(db.gitRepository.upsert).toHaveBeenCalledWith(
      expect.objectContaining({
        create: expect.objectContaining({ createdById: 'mock-profile-id' }),
      })
    );
  });

  it('should process repository event and update existing repository', async () => {
    (db.gitRepository.upsert as jest.Mock).mockResolvedValue(mockExistingRepository);
    const eventForUpdate = new GitRepositoryEvent({
      ...baseEventProps,
      repositoryEventAction: GitRepositoryEventAction.Archived,
    });
    await eventProcessorRegistry.process(eventForUpdate);
    expect(db.gitRepository.upsert).toHaveBeenCalledWith(
      expect.objectContaining({
        update: expect.objectContaining({ state: GitRepositoryState.Archived, archivedById: 'mock-profile-id' }),
        create: expect.objectContaining({ state: GitRepositoryState.Archived, archivedById: 'mock-profile-id' }),
      })
    );
  });

  it('should correctly handle Deleted event by setting state to Deleted on upsert', async () => {
    (db.gitRepository.upsert as jest.Mock).mockResolvedValue(mockExistingRepository);
    const deleteEvent = new GitRepositoryEvent({
      ...baseEventProps,
      repositoryEventAction: GitRepositoryEventAction.Deleted,
    });
    await eventProcessorRegistry.process(deleteEvent);
    expect(db.gitRepository.upsert).toHaveBeenCalledWith(
      expect.objectContaining({
        update: expect.objectContaining({ state: GitRepositoryState.Deleted, deletedById: 'mock-profile-id' }),
        create: expect.objectContaining({ state: GitRepositoryState.Deleted, deletedById: 'mock-profile-id' }),
      })
    );
  });

  it('should handle errors during database operation and log them', async () => {
    const errorMessage = 'Database error';
    (db.gitRepository.upsert as jest.Mock).mockRejectedValue(new Error(errorMessage));
    await eventProcessorRegistry.process(mockRepositoryEvent);
    expect(db.gitRepository.upsert).toHaveBeenCalledTimes(1);
    expect(logger.error).toHaveBeenCalledWith(
      `Error processing repository ${mockRepositoryEvent.repository.name} for workspace ${mockRepositoryEvent.workspaceId}:`,
      expect.any(Error)
    );
  });

  it('should process event without author by not setting author-related IDs', async () => {
    const eventWithoutAuthor = new GitRepositoryEvent({
      ...baseEventProps,
      author: undefined as any,
    });
    (db.gitRepository.upsert as jest.Mock).mockResolvedValue(mockExistingRepository);
    const { createIntegrationProfile } = require('@/lib/profile/integration-profile-creator');
    (createIntegrationProfile as jest.Mock).mockImplementation(async (author) => {
      if (!author) return undefined;
      return { id: 'mock-profile-id-for-author' };
    });

    await eventProcessorRegistry.process(eventWithoutAuthor);

    expect(db.gitRepository.upsert).toHaveBeenCalledWith(
      expect.objectContaining({
        create: expect.objectContaining({ createdById: undefined, deletedById: undefined, archivedById: undefined }),
        update: expect.not.objectContaining({
          createdById: expect.anything(),
          deletedById: expect.anything(),
          archivedById: expect.anything(),
        }),
      })
    );
  });

  it('should throw error if workspaceId is missing', async () => {
    const eventWithoutWorkspaceId = new GitRepositoryEvent({
      ...baseEventProps,
      workspaceId: undefined as any,
    });
    await expect(eventProcessorRegistry.process(eventWithoutWorkspaceId)).rejects.toThrow(
      'Workspace ID is required for repository event processing'
    );
    expect(db.gitRepository.upsert).not.toHaveBeenCalled();
  });

  it('should throw error if integrationId is missing', async () => {
    const eventWithoutIntegrationId = new GitRepositoryEvent({
      ...baseEventProps,
      integrationId: undefined as any,
    });
    await expect(eventProcessorRegistry.process(eventWithoutIntegrationId)).rejects.toThrow(
      'Integration ID is required for repository event processing'
    );
    expect(db.gitRepository.upsert).not.toHaveBeenCalled();
  });

  it('should process repository event correctly', async () => {
    (db.gitRepository.upsert as jest.Mock).mockResolvedValue(mockExistingRepository);
    await eventProcessorRegistry.process(mockRepositoryEvent);
    expect(db.gitRepository.upsert).toHaveBeenCalledTimes(1);
  });
});
