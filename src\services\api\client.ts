'use client';

import { getCookie } from 'cookies-next';
import { getAuth } from 'firebase/auth';

import { CoreApiService, TokenProvider, WorkspaceProvider } from './core';

/**
 * Client-side token provider that gets the token from Firebase Auth
 */
class ClientTokenProvider implements TokenProvider {
  /**
   * Get the authentication token from the current Firebase user
   * @returns A promise that resolves to the token or undefined
   */
  async getToken(): Promise<string | undefined> {
    try {
      const auth = getAuth();
      const user = auth.currentUser;

      if (user) {
        return await user.getIdToken();
      }

      return undefined;
    } catch (error) {
      console.error('Error getting Firebase ID token:', error);
      return undefined;
    }
  }
}

/**
 * Client-side workspace provider that gets the workspace ID from cookies
 */
class ClientWorkspaceProvider implements WorkspaceProvider {
  /**
   * Get the current workspace ID from cookies
   * @returns A promise that resolves to the workspace ID or undefined
   */
  async getWorkspaceId(): Promise<string | undefined> {
    try {
      const workspaceId = getCookie('selected-workspace');
      return workspaceId?.toString();
    } catch (error) {
      console.error('Error getting workspace ID from cookies:', error);
      return undefined;
    }
  }
}

/**
 * Client-side API service
 * This service is used in client components and gets the token from Firebase Auth
 */
export class ClientApiService extends CoreApiService {
  /**
   * Create a new ClientApiService
   * @param baseURL Optional base URL for API requests
   */
  constructor(baseURL?: string) {
    super(new ClientTokenProvider(), new ClientWorkspaceProvider(), baseURL);
  }
}

// Singleton instance
let clientApiInstance: ClientApiService | null = null;

/**
 * Get the client API service instance
 * @param baseURL Optional base URL for API requests
 * @returns The client API service instance
 */
export function getClientApiService(baseURL?: string): ClientApiService {
  if (!clientApiInstance) {
    clientApiInstance = new ClientApiService(baseURL);
  }
  return clientApiInstance;
}

/**
 * Reset the client API service instance
 * This is useful when logging out to ensure a fresh instance is created for the next user
 */
export function resetClientApiService(): void {
  clientApiInstance = null;
}

export default getClientApiService;
