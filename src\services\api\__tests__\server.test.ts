import * as cookiesNext from 'cookies-next';

import { CoreApiService } from '../core';
import { getServerApiService, ServerApiService } from '../server';

// Create a mock CoreApiService class
class MockCoreApiService {
  get = jest.fn();
  post = jest.fn();
  put = jest.fn();
  patch = jest.fn();
  delete = jest.fn();
  request = jest.fn();
}

// Mock the CoreApiService
jest.mock('../core', () => {
  return {
    CoreApiService: jest.fn().mockImplementation(() => {
      return new MockCoreApiService();
    }),
  };
});

describe('ServerApiService', () => {
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
  });

  it('should create a ServerApiService instance', () => {
    const apiService = new ServerApiService();
    expect(apiService).toBeDefined();
    expect(CoreApiService).toHaveBeenCalled();
  });

  it('should create a new instance each time getServerApiService is called', () => {
    const instance1 = getServerApiService();
    const instance2 = getServerApiService();

    // Server API service should not use a singleton pattern
    expect(instance1).not.toBe(instance2);
    expect(CoreApiService).toHaveBeenCalledTimes(2);
  });

  it('should get token from cookies', async () => {
    // Mock the getCookie function from cookies-next
    jest.spyOn(cookiesNext, 'getCookie').mockReturnValue('mock-session-token');

    // Create a new instance of ServerApiService
    new ServerApiService();

    // Extract the token provider from the constructor call
    const tokenProvider = (CoreApiService as jest.Mock).mock.calls[0][0];

    // Call the getToken method
    const token = await tokenProvider.getToken();

    // Verify that the token is correct
    expect(token).toBe('mock-session-token');

    // Restore the original implementation
    jest.restoreAllMocks();
  });
});
