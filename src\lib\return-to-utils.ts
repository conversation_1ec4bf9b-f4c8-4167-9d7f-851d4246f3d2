/**
 * Utility functions for handling URL navigation with returnTo parameter preservation
 */

/**
 * Builds an authentication URL with returnTo parameter preserved if present
 * @param basePath - The base authentication path (e.g., '/auth/sign-in')
 * @param returnTo - The returnTo parameter value (if any)
 * @returns The URL with returnTo parameter appended if present
 */
export function getPathWithReturnTo(basePath: string, returnTo?: string | null): string {
  if (!returnTo) {
    return basePath;
  }

  return `${basePath}?returnTo=${encodeURIComponent(returnTo)}`;
}

/**
 * Extracts the returnTo parameter from URLSearchParams
 * @param searchParams - URL search parameters
 * @returns The returnTo parameter value or null if not present
 */
export function getReturnToParam(searchParams: URLSearchParams): string | null {
  return searchParams.get('returnTo');
}
