import 'server-only';

import { App } from '@octokit/app';

class GitHubAppSingleton {
  private static instance: App | null = null;

  private constructor() {}

  /**
   * Gets the GitHub App singleton instance.
   * The instance is lazily loaded when this method is first called.
   * The instance is created with the following configuration:
   * - appId: process.env.GITHUB_APP_ID
   * - privateKey: process.env.GITHUB_APP_PRIVATE_KEY
   * - oauth.clientId: process.env.GITHUB_CLIENT_ID
   * - oauth.clientSecret: process.env.GITHUB_CLIENT_SECRET
   * - webhooks.secret: process.env.GITHUB_WEBHOOK_SECRET
   * @returns The GitHub App instance.
   */
  public static getInstance(): App {
    if (!GitHubAppSingleton.instance) {
      GitHubAppSingleton.instance = new App({
        appId: process.env.GITHUB_APP_ID || '',
        privateKey: process.env.GITHUB_APP_PRIVATE_KEY || '',
        oauth: {
          clientId: process.env.GITHUB_CLIENT_ID || '',
          clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
        },
        webhooks: {
          secret: process.env.GITHUB_WEBHOOK_SECRET || '',
        },
      });
    }

    return GitHubAppSingleton.instance;
  }

  public static clear() {
    GitHubAppSingleton.instance = null;
  }
}

/**
 * Gets the GitHub App singleton instance.
 * @returns The GitHub App instance.
 */
export const getInstance = () => GitHubAppSingleton.getInstance();
