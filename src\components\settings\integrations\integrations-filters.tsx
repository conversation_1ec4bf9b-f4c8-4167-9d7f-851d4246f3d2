'use client';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import { ArrowClockwiseIcon } from '@phosphor-icons/react/dist/ssr/ArrowClockwise';
import { MagnifyingGlassIcon } from '@phosphor-icons/react/dist/ssr/MagnifyingGlass';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { ViewMode, ViewToggle } from './view-toggle';

interface IntegrationsFiltersProps {
  searchQuery: string;
  onSearchChangeAction: (_: string) => void;
  viewMode: ViewMode;
  onViewModeChangeAction: (_: ViewMode) => void;
  onRefreshAction?: () => void;
  isRefreshing?: boolean;
}

export function IntegrationsFilters({
  searchQuery,
  onSearchChangeAction,
  viewMode,
  onViewModeChangeAction,
  onRefreshAction,
  isRefreshing = false,
}: IntegrationsFiltersProps): React.JSX.Element {
  const t = useTranslations('settings.integrations');

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChangeAction(event.target.value);
  };

  return (
    <Card sx={{ p: 2 }}>
      <Stack
        direction={{ xs: 'column', sm: 'row' }}
        spacing={2}
        alignItems={{ xs: 'stretch', sm: 'center' }}
        justifyContent='space-between'
      >
        <OutlinedInput
          value={searchQuery}
          onChange={handleSearchChange}
          fullWidth
          placeholder={t('search')}
          startAdornment={
            <InputAdornment position='start'>
              <MagnifyingGlassIcon fontSize='var(--icon-fontSize-md)' />
            </InputAdornment>
          }
          sx={{ maxWidth: { sm: '500px' } }}
        />
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {onRefreshAction && (
            <Tooltip title={t('refresh')}>
              <IconButton
                onClick={onRefreshAction}
                disabled={isRefreshing}
                sx={{
                  animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
                  '@keyframes spin': {
                    '0%': {
                      transform: 'rotate(0deg)',
                    },
                    '100%': {
                      transform: 'rotate(360deg)',
                    },
                  },
                }}
              >
                <ArrowClockwiseIcon fontSize='var(--icon-fontSize-md)' />
              </IconButton>
            </Tooltip>
          )}
          <ViewToggle view={viewMode} onChangeAction={onViewModeChangeAction} />
        </Box>
      </Stack>
    </Card>
  );
}
