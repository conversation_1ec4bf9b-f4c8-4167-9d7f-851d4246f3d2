import { Workspace, WorkspaceMembership } from '@prisma/client';

import { CoreApiService } from './core';

/**
 * Workspace API service
 * This service provides methods for interacting with the workspace API
 */
export class WorkspaceApiService {
  private api: CoreApiService;

  /**
   * Create a new WorkspaceApiService
   * @param apiService The API service to use
   */
  constructor(apiService: CoreApiService) {
    this.api = apiService;
  }

  /**
   * Get all workspaces for the current user
   * @returns A promise that resolves to an array of workspaces
   */
  async getWorkspaces(): Promise<Workspace[]> {
    return await this.api.request({
      method: 'GET',
      url: '/workspace',
    });
  }

  /**
   * Get a workspace by ID
   * @param id The workspace ID
   * @returns A promise that resolves to the workspace
   */
  async getWorkspace(id: string): Promise<Workspace> {
    return await this.api.request({
      method: 'GET',
      url: `/workspace/${id}`,
    });
  }

  /**
   * Create a new workspace
   * @param data The workspace data
   * @returns A promise that resolves to the created workspace
   */
  async createWorkspace(data: { name: string; avatar?: string }): Promise<Workspace> {
    return await this.api.request({
      method: 'POST',
      url: '/workspace',
      data,
    });
  }

  /**
   * Update a workspace
   * @param id The workspace ID
   * @param data The workspace data to update
   * @returns A promise that resolves to the updated workspace
   */
  async updateWorkspace(id: string, data: { name?: string; avatar?: string }): Promise<Workspace> {
    return await this.api.request({
      method: 'PATCH',
      url: `/workspace/${id}`,
      data,
    });
  }

  /**
   * Delete a workspace
   * @param workspaceId The workspace ID
   * @returns A promise that resolves when the workspace is deleted
   */
  async deleteWorkspace(workspaceId: string): Promise<void> {
    await this.api.request({
      method: 'DELETE',
      url: `/workspace/${workspaceId}`,
    });
  }

  /**
   * Get workspace memberships for the current user
   * @returns A promise that resolves to an array of workspace memberships
   */
  async getWorkspaceMemberships(): Promise<WorkspaceMembership[]> {
    return await this.api.request({
      method: 'GET',
      url: '/workspace/memberships',
    });
  }

  /**
   * Select a workspace as the active workspace
   * @param id The workspace ID to select
   * @returns A promise that resolves to the selected workspace
   */
  async selectWorkspace(id: string): Promise<Workspace> {
    return await this.api.request({
      method: 'POST',
      url: `/workspace/${id}/select`,
    });
  }

  /**
   * Get the currently selected workspace
   * @returns A promise that resolves to the selected workspace or null if none is selected
   */
  async getSelectedWorkspace(): Promise<Workspace | null> {
    try {
      return await this.api.request({
        method: 'GET',
        url: '/workspace/selected',
      });
    } catch (error) {
      // If no workspace is selected, return null
      if ((error as any)?.response?.status === 404) {
        return null;
      }
      throw error;
    }
  }
}

/**
 * Create a workspace API service with the given API service
 * @param apiService The API service to use
 * @returns A new workspace API service
 */
export function createWorkspaceApiService(apiService: CoreApiService): WorkspaceApiService {
  return new WorkspaceApiService(apiService);
}
