'use client';

import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import InputLabel from '@mui/material/InputLabel';
import Link from '@mui/material/Link';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { EyeIcon } from '@phosphor-icons/react/dist/ssr/Eye';
import { EyeSlashIcon } from '@phosphor-icons/react/dist/ssr/EyeSlash';
import { useTranslations } from 'next-intl';
import NextLink from 'next/link';
import * as React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import { useAuth } from '@/contexts/firebase-auth-context';
import { logger } from '@/lib/logger/default-logger';
import { getPathWithReturnTo, getReturnToParam } from '@/lib/return-to-utils';
import { paths } from '@/paths';
import { useSearchParams } from 'next/navigation';

type Values = {
  email: string;
  password: string;
};

const defaultValues = { email: '', password: '' } satisfies Values;

export function SignInForm(): React.JSX.Element {
  const router = useRouter();
  const params = useSearchParams();
  const { signInWithEmail, clearError, error: authError } = useAuth();
  const t = useTranslations('auth');

  // Get returnTo parameter for preserving across navigation
  const returnTo = getReturnToParam(params);

  // Create schema with translations
  const schema = React.useMemo(() => {
    return zod.object({
      email: zod
        .string()
        .min(1, { message: t('signIn.emailRequired') })
        .email(),
      password: zod.string().min(1, { message: t('signIn.passwordRequired') }),
    });
  }, [t]);

  const [showPassword, setShowPassword] = React.useState<boolean>();
  const [isPending, setIsPending] = React.useState<boolean>(false);
  const [errorDismissed, setErrorDismissed] = React.useState<boolean>(false);
  const [formErrorDismissed, setFormErrorDismissed] = React.useState<boolean>(false);

  const {
    control,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<Values>({ defaultValues, resolver: zodResolver(schema) });

  const onSubmit = React.useCallback(
    async (values: Values): Promise<void> => {
      setIsPending(true);
      // Reset error dismissed states on new submission
      setErrorDismissed(false);
      setFormErrorDismissed(false);

      try {
        // Sign in with Firebase
        await signInWithEmail(values.email, values.password);

        if (returnTo) {
          router.replace(returnTo as any);
          return;
        }

        // Redirect to start after successful login
        router.push(paths.home);
      } catch (error) {
        // Parse Firebase error message and provide a user-friendly message
        let errorMessage = t('errors.invalidCredentials');

        // Only in development, log the actual error
        if (process.env.NODE_ENV === 'development') {
          logger.warn('Sign in error:', error);
        }

        // Handle specific error codes if needed
        const firebaseError = error as { code?: string; message: string };
        if (firebaseError.code === 'auth/user-not-found' || firebaseError.code === 'auth/wrong-password') {
          errorMessage = t('errors.invalidCredentials');
        } else if (firebaseError.code === 'auth/too-many-requests') {
          errorMessage = t('errors.authFailed');
        } else if (firebaseError.code === 'auth/user-disabled') {
          errorMessage = t('errors.authFailed');
        }

        setError('root', { type: 'server', message: errorMessage });
        setIsPending(false);
      }
    },
    [signInWithEmail, router, setError, setErrorDismissed, setFormErrorDismissed, returnTo, t]
  );

  return (
    <Stack spacing={4}>
      <Stack spacing={1}>
        <Typography variant='h4'>{t('signIn.title')}</Typography>
        <Typography color='text.secondary' variant='body2'>
          {t('signIn.noAccount')}{' '}
          <Link
            component={NextLink}
            href={getPathWithReturnTo(paths.auth.signUp, returnTo)}
            underline='hover'
            variant='subtitle2'
          >
            {t('signIn.signUpLink')}
          </Link>
        </Typography>
      </Stack>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={2}>
          <Controller
            control={control}
            name='email'
            render={({ field }) => (
              <FormControl error={Boolean(errors.email)}>
                <InputLabel>{t('signIn.emailLabel')}</InputLabel>
                <OutlinedInput {...field} label={t('signIn.emailLabel')} type='email' />
                {errors.email ? <FormHelperText>{errors.email.message}</FormHelperText> : null}
              </FormControl>
            )}
          />
          <Controller
            control={control}
            name='password'
            render={({ field }) => (
              <FormControl error={Boolean(errors.password)}>
                <InputLabel>{t('signIn.passwordLabel')}</InputLabel>
                <OutlinedInput
                  {...field}
                  endAdornment={
                    showPassword ? (
                      <EyeIcon
                        cursor='pointer'
                        fontSize='var(--icon-fontSize-md)'
                        onClick={(): void => {
                          setShowPassword(false);
                        }}
                      />
                    ) : (
                      <EyeSlashIcon
                        cursor='pointer'
                        fontSize='var(--icon-fontSize-md)'
                        onClick={(): void => {
                          setShowPassword(true);
                        }}
                      />
                    )
                  }
                  label={t('signIn.passwordLabel')}
                  type={showPassword ? 'text' : 'password'}
                />
                {errors.password ? <FormHelperText>{errors.password.message}</FormHelperText> : null}
              </FormControl>
            )}
          />
          <div>
            <Link
              component={NextLink}
              href={getPathWithReturnTo(paths.auth.resetPassword, returnTo)}
              variant='subtitle2'
            >
              {t('signIn.forgotPassword')}
            </Link>
          </div>
          {/* Display form validation errors */}
          {errors.root && !formErrorDismissed ? (
            <Alert
              color='error'
              severity='error'
              sx={{ mb: 2 }}
              onClose={() => {
                // We can't directly clear form errors from react-hook-form
                // But we can mark the error as dismissed so it doesn't show
                setFormErrorDismissed(true);
                clearError();
              }}
            >
              {errors.root.message}
            </Alert>
          ) : null}

          {/* Display Firebase authentication errors */}
          {authError && !errors.root && !errorDismissed ? (
            <Alert
              color='error'
              severity='error'
              sx={{ mb: 2 }}
              onClose={() => {
                clearError();
                setErrorDismissed(true);
              }}
            >
              {authError.includes('auth/') ? t('errors.invalidCredentials') : t('errors.authFailed')}
            </Alert>
          ) : null}
          <Button disabled={isPending} type='submit' variant='contained'>
            {t('signIn.submitButton')}
          </Button>
        </Stack>
      </form>
    </Stack>
  );
}
