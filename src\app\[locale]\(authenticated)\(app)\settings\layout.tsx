'use client';

import { Link, usePathname } from '@/i18n/navigation';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { BellIcon } from '@phosphor-icons/react/dist/ssr/Bell';
import { CreditCardIcon } from '@phosphor-icons/react/dist/ssr/CreditCard';
import { GearSixIcon } from '@phosphor-icons/react/dist/ssr/GearSix';
import { PaintBrushIcon } from '@phosphor-icons/react/dist/ssr/PaintBrush';
import { PlugsConnectedIcon } from '@phosphor-icons/react/dist/ssr/PlugsConnected';
import { UserIcon } from '@phosphor-icons/react/dist/ssr/User';
import { UsersThreeIcon } from '@phosphor-icons/react/dist/ssr/UsersThree';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { paths } from '@/paths';

interface LayoutProps {
  children: React.ReactNode;
}

interface SettingsItem {
  label: string;
  value: string;
  icon: React.ReactNode;
  path: string;
}

interface SettingsCategory {
  title: string;
  items: SettingsItem[];
}

export default function Layout({ children }: LayoutProps): React.JSX.Element {
  const pathname = usePathname();
  const t = useTranslations('settings');

  const categories: SettingsCategory[] = [
    {
      title: t('categories.personal'),
      items: [
        {
          label: t('tabs.account'),
          value: 'account',
          icon: <UserIcon fontSize='var(--icon-fontSize-md)' />,
          path: paths.settings.account,
        },
        {
          label: t('tabs.preferences'),
          value: 'preferences',
          icon: <PaintBrushIcon fontSize='var(--icon-fontSize-md)' />,
          path: paths.settings.preferences,
        },
        {
          label: t('tabs.notifications'),
          value: 'notifications',
          icon: <BellIcon fontSize='var(--icon-fontSize-md)' />,
          path: paths.settings.notifications,
        },
        {
          label: t('tabs.security'),
          value: 'security',
          icon: <GearSixIcon fontSize='var(--icon-fontSize-md)' />,
          path: paths.settings.security,
        },
      ],
    },
    {
      title: t('categories.workspace'),
      items: [
        {
          label: t('tabs.billing'),
          value: 'billing',
          icon: <CreditCardIcon fontSize='var(--icon-fontSize-md)' />,
          path: paths.settings.billing,
        },
        {
          label: t('tabs.team'),
          value: 'team',
          icon: <UsersThreeIcon fontSize='var(--icon-fontSize-md)' />,
          path: paths.settings.team,
        },
        {
          label: t('tabs.integrations'),
          value: 'integrations',
          icon: <PlugsConnectedIcon fontSize='var(--icon-fontSize-md)' />,
          path: paths.settings.integrations.index,
        },
      ],
    },
  ];

  return (
    <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' } }}>
      {/* Left sidebar */}
      <Box
        sx={{
          width: { xs: '100%', md: 280 },
          flexShrink: 0,
          py: 2,
          position: { md: 'sticky' },
          top: { md: 0 },
          maxHeight: { md: '100vh' },
          overflowY: 'auto',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', px: 3, mb: 2 }}>
          <Typography variant='h5' sx={{ flexGrow: 1 }}>
            {t('title')}
          </Typography>
        </Box>

        {categories.map((category, index) => (
          <React.Fragment key={category.title}>
            <Typography variant='overline' color='text.secondary' sx={{ px: 3, py: 1.5, display: 'block' }}>
              {category.title}
            </Typography>
            <Stack spacing={0.5} sx={{ mb: 2 }}>
              {category.items.map((item) => {
                const isActive = pathname.startsWith(item.path);
                return (
                  <Box
                    key={item.value}
                    sx={{
                      mx: 2,
                    }}
                  >
                    <Box
                      component={Link}
                      href={item.path as any} // Type assertion to fix TypeScript error
                      sx={{
                        alignItems: 'center',
                        borderRadius: 1,
                        display: 'flex',
                        justifyContent: 'flex-start',
                        pl: '16px',
                        pr: '16px',
                        py: '10px',
                        textDecoration: 'none',
                        ...(isActive && {
                          backgroundColor: 'var(--mui-palette-action-selected)',
                        }),
                        '&:hover': {
                          backgroundColor: 'var(--mui-palette-action-hover)',
                        },
                      }}
                    >
                      <Box
                        sx={{
                          alignItems: 'center',
                          display: 'flex',
                          justifyContent: 'center',
                          mr: 2,
                          color: isActive ? 'primary.main' : 'text.secondary',
                        }}
                      >
                        {item.icon}
                      </Box>
                      <Typography
                        sx={{
                          color: isActive ? 'primary.main' : 'text.primary',
                          fontWeight: isActive ? 600 : 400,
                          flexGrow: 1,
                          fontSize: '0.875rem',
                        }}
                      >
                        {item.label}
                      </Typography>
                    </Box>
                  </Box>
                );
              })}
            </Stack>
            {index < categories.length - 1 && <Divider sx={{ mx: 2, mb: 2 }} />}
          </React.Fragment>
        ))}
      </Box>

      {/* Main content */}
      <Box
        sx={{
          flexGrow: 1,
          p: 3,
        }}
      >
        {children}
      </Box>
    </Box>
  );
}
