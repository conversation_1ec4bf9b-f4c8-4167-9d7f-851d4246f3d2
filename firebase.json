{"apphosting": {"backendId": "pulse", "rootDir": ".", "ignore": ["node_modules", ".git", "prisma", "nginx", "docs", ".github", ".swc", ".vscode", "gcp", "database", "firebase-debug.log", "firebase-debug.*.log", "functions"]}, "emulators": {"auth": {"port": 9099}, "ui": {"enabled": true, "port": 4000}, "storage": {"port": 9199}, "singleProjectMode": true}, "storage": {"rules": "gcp/storage/storage.rules"}}