import Grid from '@mui/material/Grid';
import dayjs from 'dayjs';
import type { Metadata } from 'next';
import * as React from 'react';

import { LatestOrders } from '@/components/git/overview/latest-orders';
import { LatestProducts } from '@/components/git/overview/latest-products';
import { PullRequests } from '@/components/git/overview/pull-requests';
import { Sales } from '@/components/git/overview/sales';
import { TasksProgress } from '@/components/git/overview/tasks-progress';
import { TotalCommits } from '@/components/git/overview/total-commits';
import { TotalProfit } from '@/components/git/overview/total-profit';
import { Traffic } from '@/components/git/overview/traffic';
import { config } from '@/config';

export const metadata = {
  title: `GitHub Productivity | ${config.site.name}`,
} satisfies Metadata;

export default function Page(): React.JSX.Element {
  return (
    <Grid container spacing={3}>
      <Grid size={{ lg: 3, sm: 6, xs: 12 }}>
        <TotalCommits
          diff={12}
          trend='up'
          sx={{ height: '100%' }}
          value='1,024'
          chartData={[5, 7, 3, 8, 10, 6, 4, 9, 12, 8, 7, 5, 6, 8, 9, 11, 7, 5, 3, 6, 8, 10, 12, 9, 7, 5, 8, 11, 13, 10]}
        />
      </Grid>
      <Grid size={{ lg: 3, sm: 6, xs: 12 }}>
        <PullRequests
          diff={16}
          trend='up'
          sx={{ height: '100%' }}
          value='48'
          chartData={[2, 3, 1, 4, 2, 5, 3, 2, 4, 3, 2, 1, 3, 2, 4, 5, 3, 2, 1, 2, 3, 4, 2, 3, 1, 2, 3, 4, 5, 2]}
        />
      </Grid>
      <Grid size={{ lg: 3, sm: 6, xs: 12 }}>
        <TasksProgress sx={{ height: '100%' }} value={82.5} />
      </Grid>
      <Grid size={{ lg: 3, sm: 6, xs: 12 }}>
        <TotalProfit sx={{ height: '100%' }} suggestions={1250} acceptances={850} />
      </Grid>
      <Grid size={{ lg: 8, xs: 12 }}>
        <Sales
          chartSeries={[
            {
              name: 'Commits',
              data: [5, 7, 3, 8, 10, 6, 4, 9, 12, 8, 7, 5, 6, 8, 9, 11, 7, 5, 3, 6, 8, 10, 12, 9, 7, 5, 8, 11, 13, 10],
            },
          ]}
          sx={{ height: '100%' }}
        />
      </Grid>
      <Grid size={{ lg: 4, md: 6, xs: 12 }}>
        <Traffic chartSeries={[45, 35, 20]} labels={['JavaScript', 'TypeScript', 'CSS']} sx={{ height: '100%' }} />
      </Grid>
      <Grid size={{ lg: 4, md: 6, xs: 12 }}>
        <LatestProducts
          products={[
            {
              id: 'REPO-005',
              name: 'frontend',
              updatedAt: dayjs().subtract(18, 'minutes').subtract(5, 'hour').toDate(),
            },
            {
              id: 'REPO-004',
              name: 'backend-api',
              updatedAt: dayjs().subtract(41, 'minutes').subtract(3, 'hour').toDate(),
            },
            {
              id: 'REPO-003',
              name: 'mobile-app',
              updatedAt: dayjs().subtract(5, 'minutes').subtract(3, 'hour').toDate(),
            },
            {
              id: 'REPO-002',
              name: 'documentation',
              updatedAt: dayjs().subtract(23, 'minutes').subtract(2, 'hour').toDate(),
            },
            {
              id: 'REPO-001',
              name: 'shared-components',
              updatedAt: dayjs().subtract(10, 'minutes').toDate(),
            },
          ]}
          sx={{ height: '100%' }}
        />
      </Grid>
      <Grid size={{ lg: 8, md: 12, xs: 12 }}>
        <LatestOrders
          orders={[
            {
              id: 'PR-007',
              customer: { name: 'Ekaterina Tankova' },
              amount: 30.5,
              status: 'pending',
              createdAt: dayjs().subtract(10, 'minutes').toDate(),
            },
            {
              id: 'PR-006',
              customer: { name: 'Cao Yu' },
              amount: 25.1,
              status: 'delivered',
              createdAt: dayjs().subtract(10, 'minutes').toDate(),
            },
            {
              id: 'PR-004',
              customer: { name: 'Alexa Richardson' },
              amount: 10.99,
              status: 'refunded',
              createdAt: dayjs().subtract(10, 'minutes').toDate(),
            },
            {
              id: 'PR-003',
              customer: { name: 'Anje Keizer' },
              amount: 96.43,
              status: 'pending',
              createdAt: dayjs().subtract(10, 'minutes').toDate(),
            },
            {
              id: 'PR-002',
              customer: { name: 'Clarke Gillebert' },
              amount: 32.54,
              status: 'delivered',
              createdAt: dayjs().subtract(10, 'minutes').toDate(),
            },
            {
              id: 'PR-001',
              customer: { name: 'Adam Denisov' },
              amount: 16.76,
              status: 'delivered',
              createdAt: dayjs().subtract(10, 'minutes').toDate(),
            },
          ]}
          sx={{ height: '100%' }}
        />
      </Grid>
    </Grid>
  );
}
