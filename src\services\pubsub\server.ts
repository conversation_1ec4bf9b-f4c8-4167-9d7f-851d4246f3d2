import 'server-only';

import { logger } from '@/lib/logger/default-logger';
import { CreateSubscriptionOptions, PubSub } from '@google-cloud/pubsub';

export const pubSub = new PubSub();

export function getDeadLetterName(subscriptionName: string) {
  return subscriptionName + `-dead-letter`;
}

export async function createTopicAndSubscription(topicName: string, subscriptionName: string) {
  if (!topicName || !subscriptionName) {
    throw new Error('Invalid topic or subscription name');
  }

  const deadLetterName = getDeadLetterName(subscriptionName);

  await createTopic(topicName);
  await createTopic(deadLetterName);

  await createSubscription(subscriptionName, topicName);
  await createSubscription(deadLetterName, deadLetterName, false);
}

export async function createSubscription(subscriptionName: string, topicName: string, enableDeadLetter = true) {
  if (await pubSub.subscription(subscriptionName).exists()) {
    return false;
  }

  const options: CreateSubscriptionOptions = {
    deadLetterPolicy: enableDeadLetter
      ? {
          deadLetterTopic: getDeadLetterName(subscriptionName),
          maxDeliveryAttempts: 10,
        }
      : null,
  };

  logger.info('Creating PubSub subscription', subscriptionName, 'on topic', topicName);
  await pubSub.createSubscription(topicName, subscriptionName, options);
  logger.info('PubSub subscription', subscriptionName, 'created on topic', topicName);

  return true;
}

export async function createTopic(topicName: string) {
  if (await pubSub.topic(topicName).exists()) {
    return false;
  }

  logger.info('Creating PubSub topic', topicName);
  await pubSub.createTopic(topicName);
  logger.info('PubSub topic', topicName, 'created');

  return true;
}
