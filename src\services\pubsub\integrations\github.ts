import 'server-only';

import { logger } from '@/lib/logger/default-logger';
import { isUniqueConstraintError } from '@/lib/prisma/prisma';
import { db } from '@/services/db';
import { Message } from '@google-cloud/pubsub';
import { createTopicAndSubscription, getDeadLetterName, pubSub } from '../server';

export async function initializeGitHubWebhookProcessor() {
  const topic = process.env.PUBSUB_GITHUB_TOPIC;
  const subscription = process.env.PUBSUB_GITHUB_SUBSCRIPTION;

  try {
    if (!topic || !subscription) {
      throw new Error('Missing PUBSUB_GITHUB_TOPIC or PUBSUB_GITHUB_SUBSCRIPTION environment variables');
    }

    await createTopicAndSubscription(topic, subscription);

    pubSub.subscription(subscription).on('message', processMessage);
    pubSub.subscription(getDeadLetterName(subscription)).on('message', processMessage);

    logger.info('GitHub webhook processor initialized');
  } catch (error) {
    logger.error('Error initializing GitHub webhook processor. Trying again in 5 seconds', error);

    setTimeout(async () => {
      await initializeGitHubWebhookProcessor();
    }, 5000);
  }
}

async function processMessage(message: Message) {
  logger.debug('Processing GitHub webhook message', message.id);

  const data = JSON.parse(message.data.toString('utf8'));

  const installationId = parseInt(data?.installation?.id);
  const installationAccountId = parseInt(data?.installation?.account?.id);
  const installationAccountLogin = data?.installation?.account?.login;

  try {
    const creation = db.gitHubWebhook.create({
      data: {
        delivery: message.attributes?.delivery,
        event: message.attributes?.event,
        hookId: message.attributes?.hookId,
        hookInstallationTargetType: message.attributes?.hookInstallationTargetType,
        hookInstallationTargetId: message.attributes?.hookInstallationTargetId,
        installationId,
        installationAccountId,
        installationAccountLogin,
        data,
      },
    });

    // TODO: process webhooks here

    // Await github webhook insert to database
    await creation;

    message.ack();
  } catch (error) {
    if (!isUniqueConstraintError(error)) {
      logger.error('Error processing GitHub webhook message', error);

      message.nack();
    }
  }
}
