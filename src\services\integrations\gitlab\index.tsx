import {
  Integration,
  IntegrationHandler,
  IntegrationInstallProps,
  IntegrationUninstallProps,
} from '@/services/integrations/integration';
import { IntegrationChannel } from '@prisma/client';

// GitLab integration data
const gitlabIntegration: Integration = {
  id: IntegrationChannel.GitLab,
  title: 'GitLab',
  status: 'coming-soon',
  capabilities: ['pullRequests', 'codeReview', 'cicdPipelines', 'issueTracking', 'teamPerformance'],
};

// GitLab integration handler
export const gitlabHandler: IntegrationHandler = {
  // Return the integration data
  getIntegration: () => {
    return gitlabIntegration;
  },

  // Handle installation (not implemented for coming soon)
  onInstallAction: async (_props: IntegrationInstallProps) => {
    console.log('Installing GitLab integration');
    // Not implemented for coming soon
    return false;
  },

  // Handle uninstallation (not implemented for coming soon)
  onUninstallAction: async (_props: IntegrationUninstallProps) => {
    console.log('Uninstalling GitLab integration');
    // Not implemented for coming soon
    return false;
  },

  // This integration doesn't have a custom install flow
  hasCustomInstallFlow: () => false,
};
