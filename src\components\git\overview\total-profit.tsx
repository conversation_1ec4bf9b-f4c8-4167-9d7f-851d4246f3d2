import Avatar from '@mui/material/Avatar';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Stack from '@mui/material/Stack';
import type { SxProps } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { RobotIcon } from '@phosphor-icons/react/dist/ssr/Robot';
import { useTranslations } from 'next-intl';
import * as React from 'react';

export interface CopilotMetricsProps {
  sx?: SxProps;
  suggestions: number;
  acceptances: number;
}

export interface CopilotMetricsProps {
  sx?: SxProps;
  suggestions: number;
  acceptances: number;
  timeSpan?: string; // Optional time span for the metrics (e.g., "Last 7 days")
}

export function TotalProfit({ suggestions, acceptances, timeSpan, sx }: CopilotMetricsProps): React.JSX.Element {
  const t = useTranslations('git.overview');

  // Calculate the acceptance percentage
  const acceptancePercentage = Math.round((acceptances / suggestions) * 100);

  // Calculate the rejection count and percentage
  const rejections = suggestions - acceptances;
  const rejectionPercentage = 100 - acceptancePercentage;

  return (
    <Card sx={sx}>
      <CardContent>
        <Stack spacing={2}>
          <Stack direction='row' sx={{ alignItems: 'flex-start', justifyContent: 'space-between' }}>
            <Typography color='text.secondary' variant='overline'>
              {t('metrics.copilotMetrics')}
            </Typography>
            <Avatar
              sx={{
                backgroundColor: 'var(--mui-palette-primary-main)',
                height: '40px',
                width: '40px',
              }}
            >
              <RobotIcon fontSize='var(--icon-fontSize-md)' />
            </Avatar>
          </Stack>

          <Stack spacing={1}>
            <Stack direction='row' justifyContent='space-between' alignItems='center'>
              <Typography variant='body2' color='text.secondary'>
                {t('copilot.suggestions')}
              </Typography>
              <Typography variant='body1' fontWeight='medium'>
                {suggestions}
              </Typography>
            </Stack>

            <Stack direction='row' justifyContent='space-between' alignItems='center'>
              <Typography variant='body2' color='text.secondary'>
                {t('copilot.acceptances')}
              </Typography>
              <Typography variant='body1' fontWeight='medium'>
                {acceptances} ({acceptancePercentage}%)
              </Typography>
            </Stack>

            <Stack direction='row' justifyContent='space-between' alignItems='center'>
              <Typography variant='body2' color='text.secondary'>
                {t('copilot.rejections')}
              </Typography>
              <Typography variant='body1' fontWeight='medium'>
                {rejections} ({rejectionPercentage}%)
              </Typography>
            </Stack>

            <Typography variant='caption' color='text.secondary' sx={{ pt: 1, textAlign: 'right' }}>
              {timeSpan ? timeSpan : t('timeframes.lastDays', { days: 7 })}
            </Typography>
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
}
