'use client';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { InfoIcon, LinkIcon } from '@phosphor-icons/react';
import { useTranslations } from 'next-intl';
import * as React from 'react';

export function GitHubDoc(): React.JSX.Element {
  const t = useTranslations('settings.integrations.github');

  return (
    <Paper
      variant='outlined'
      sx={{
        p: 2,
        mb: 3,
        bgcolor: 'background.default',
        borderColor: 'divider',
      }}
    >
      <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-start' }}>
        <InfoIcon size={24} weight='fill' style={{ marginTop: '2px' }} />
        <Box>
          <Typography variant='body2' color='text.secondary' whiteSpace='pre-wrap' sx={{ mb: 1 }}>
            {t('installationsInfo')}
          </Typography>
          <Link
            href='https://docs.github.com/en/apps/using-github-apps/installing-a-github-app-from-a-third-party'
            target='_blank'
            rel='noopener noreferrer'
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              fontSize: '0.875rem',
              gap: 0.5,
              width: 'fit-content',
            }}
          >
            {t('installationDocs')}
            <LinkIcon size={14} />
          </Link>
        </Box>
      </Box>
    </Paper>
  );
}
