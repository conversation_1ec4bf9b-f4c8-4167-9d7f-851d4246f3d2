'use client';

import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { ClockCounterClockwiseIcon } from '@phosphor-icons/react/dist/ssr/ClockCounterClockwise';
import { CurrencyDollarIcon } from '@phosphor-icons/react/dist/ssr/CurrencyDollar';
import type { ApexOptions } from 'apexcharts';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Chart } from '@/components/core/chart';

export function CostResources(): React.JSX.Element {
  const t = useTranslations('insights');
  const theme = useTheme();

  // Sample data for cost per epic
  const costPerEpicData = {
    epics: ['User Auth', 'Dashboard', 'API v2', 'Mobile App', 'Admin Panel'],
    planned: [12000, 18000, 15000, 22000, 9000],
    actual: [13500, 16200, 17800, 24500, 8500],
  };

  // Sample data for CI/CD lost hours
  const cicdLostHoursData = {
    dates: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    failMinutes: [45, 120, 30, 180, 60, 15, 0],
    mttr: [12, 25, 8, 35, 15, 5, 0],
  };

  // Chart options for cost per epic chart
  const costPerEpicChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    colors: [theme.palette.primary.main, theme.palette.error.main],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    legend: { show: true, position: 'top' },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%',
        borderRadius: 4,
      },
    },
    stroke: { width: 0 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: costPerEpicData.epics,
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      labels: {
        formatter: (value) => `$${value / 1000}k`,
        style: { colors: theme.palette.text.secondary },
      },
    },
  };

  // Chart options for CI/CD lost hours chart
  const cicdLostHoursChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    colors: [theme.palette.error.main, theme.palette.warning.main],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    legend: { show: true, position: 'top' },
    stroke: { curve: 'smooth', width: 3 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: cicdLostHoursData.dates,
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      labels: {
        formatter: (value) => `${value} min`,
        style: { colors: theme.palette.text.secondary },
      },
    },
  };

  return (
    <Grid container spacing={3}>
      {/* Cost Per Epic Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.costPerEpic')} subheader={t('metrics.costPerStoryPoint')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={300}
                options={costPerEpicChartOptions}
                series={[
                  { name: 'Planned Cost', data: costPerEpicData.planned },
                  { name: 'Actual Cost', data: costPerEpicData.actual },
                ]}
                type='bar'
                width='100%'
              />

              <Stack direction='row' spacing={2} alignItems='center'>
                <CurrencyDollarIcon fontSize='var(--icon-fontSize-lg)' color={theme.palette.warning.main} />
                <Stack sx={{ flex: 1 }}>
                  <Typography variant='body2'>3 out of 5 epics are over budget (avg. +11.2%)</Typography>
                  <Typography variant='caption' color='text.secondary'>
                    Mobile App has the highest cost overrun (+$2,500)
                  </Typography>
                </Stack>
              </Stack>

              <Typography variant='body2' color='error'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.reviewEstimates')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* CI/CD Lost Hours Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.cicdLostHours')} subheader={t('metrics.buildFailMinutes')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={300}
                options={cicdLostHoursChartOptions}
                series={[
                  { name: 'Build Fail Minutes', data: cicdLostHoursData.failMinutes },
                  { name: 'MTTR (minutes)', data: cicdLostHoursData.mttr },
                ]}
                type='line'
                width='100%'
              />

              <Stack direction='row' spacing={2} alignItems='center'>
                <ClockCounterClockwiseIcon fontSize='var(--icon-fontSize-lg)' color={theme.palette.error.main} />
                <Stack sx={{ flex: 1 }}>
                  <Typography variant='body2'>Total lost time: 7.5 hours this week (450 minutes)</Typography>
                  <Typography variant='caption' color='text.secondary'>
                    Thursday had the highest failure rate (3 hours lost)
                  </Typography>
                </Stack>
              </Stack>

              <Typography variant='body2' color='error'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.automateRollback')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}
