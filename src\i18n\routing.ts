import { defineRouting } from 'next-intl/routing';

import { Language } from '@/lib/models/language';
import { paths } from '@/paths';

export const routing = defineRouting({
  locales: Object.values(Language),
  defaultLocale: Language.PT_BR,
  localePrefix: 'never',
  localeDetection: true,
  pathnames: {
    [paths.root]: paths.root,
    [paths.home]: {
      ['pt-BR']: '/inicio',
      es: '/inicio',
    },
    [paths.onboarding]: {
      ['pt-BR']: '/onboarding',
      es: '/onboarding',
    },
    [paths.workspaceSelection]: {
      ['pt-BR']: '/selecao-workspace',
      es: '/seleccion-workspace',
    },
    [paths.insights.delivery]: {
      ['pt-BR']: '/insights/entrega',
      es: '/insights/entrega',
    },
    [paths.insights.quality]: {
      ['pt-BR']: '/insights/qualidade',
      es: '/insights/calidad',
    },
    [paths.insights.teamHealth]: {
      ['pt-BR']: '/insights/saude-equipe',
      es: '/insights/salud-equipo',
    },
    [paths.insights.process]: {
      ['pt-BR']: '/insights/processo',
      es: '/insights/proceso',
    },
    [paths.insights.business]: {
      ['pt-BR']: '/insights/negocio',
      es: '/insights/negocio',
    },
    [paths.insights.benchmarks]: {
      ['pt-BR']: '/insights/benchmarks',
      es: '/insights/benchmarks',
    },
    [paths.insights.cost]: {
      ['pt-BR']: '/insights/custo',
      es: '/insights/costo',
    },
    [paths.customers]: {
      ['pt-BR']: '/clientes',
      es: '/clientes',
    },
    // Git section
    [paths.git.overview]: {
      ['pt-BR']: '/git/visao-geral',
      es: '/git/resumen',
    },
    [paths.git.repositories]: {
      ['pt-BR']: '/git/repositorios',
      es: '/git/repositorios',
    },
    [paths.git.pullRequests]: {
      ['pt-BR']: '/git/pull-requests',
      es: '/git/pull-requests',
    },
    [paths.git.issues]: {
      ['pt-BR']: '/git/issues',
      es: '/git/issues',
    },
    [paths.settings.integrations.index]: {
      ['pt-BR']: '/configuracoes/integracoes',
      es: '/configuracion/integraciones',
    },
    [paths.settings.integrations.details]: {
      ['pt-BR']: '/configuracoes/integracoes/detalhes',
      es: '/configuracion/integraciones/detalles',
    },
    [paths.settings.index]: {
      ['pt-BR']: '/configuracoes',
      es: '/configuracion',
    },
    [paths.settings.account]: {
      ['pt-BR']: '/configuracoes/conta',
      es: '/configuracion/cuenta',
    },
    [paths.settings.security]: {
      ['pt-BR']: '/configuracoes/seguranca',
      es: '/configuracion/seguridad',
    },
    [paths.settings.notifications]: {
      ['pt-BR']: '/configuracoes/notificacoes',
      es: '/configuracion/notificaciones',
    },
    [paths.settings.billing]: {
      ['pt-BR']: '/configuracoes/faturamento',
      es: '/configuracion/facturacion',
    },
    [paths.settings.team]: {
      ['pt-BR']: '/configuracoes/equipe',
      es: '/configuracion/equipo',
    },
    [paths.settings.preferences]: {
      ['pt-BR']: '/configuracoes/preferencias',
      es: '/configuracion/preferencias',
    },
    // Auth routes
    [paths.auth.signIn]: {
      ['pt-BR']: '/auth/entrar',
      es: '/auth/iniciar-sesion',
    },
    [paths.auth.signUp]: {
      ['pt-BR']: '/auth/cadastrar',
      es: '/auth/registrarse',
    },
    [paths.auth.resetPassword]: {
      ['pt-BR']: '/auth/redefinir-senha',
      es: '/auth/restablecer-contrasena',
    },
    // Error routes
    [paths.errors.notFound]: {
      ['pt-BR']: '/erros/nao-encontrado',
      es: '/errores/no-encontrado',
    },
  },
});
