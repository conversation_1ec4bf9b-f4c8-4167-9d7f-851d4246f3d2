'use client';

import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { LinkIcon } from '@phosphor-icons/react';
import { GitHubAccountType, IntegrationStatus } from '@prisma/client';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { paths } from '@/paths';

export interface GitHubAccount {
  accountLogin: string;
  accountType: GitHubAccountType;
  installationId: string;
}

interface ActionResultDialogProps {
  open: boolean;
  onCloseAction: () => void;
  // Suspend result props
  result:
    | {
        executedOnChannel: boolean;
        otherWorkspaces: Array<{
          id: string;
          name: string;
        }>;
      }
    | null
    | undefined;
  actionType: IntegrationStatus | null;
  // GitHub account for external link
  githubAccount?: GitHubAccount;
}

export function ActionResultDialog({
  open,
  onCloseAction,
  result,
  actionType,
  githubAccount,
}: ActionResultDialogProps): React.JSX.Element {
  const t = useTranslations('settings.integrations.github');

  const handleGoToGitHubSettings = () => {
    const account = githubAccount;
    let targetUrl = 'https://github.com/settings/installations';

    if (
      account &&
      account.accountType === GitHubAccountType.Organization &&
      account.accountLogin &&
      account.installationId
    ) {
      targetUrl = `https://github.com/organizations/${account.accountLogin}/settings/installations/${account.installationId}`;
    } else if (account && account.installationId) {
      targetUrl = `https://github.com/settings/installations`;
    }
    window.open(targetUrl, '_blank');
  };

  const renderActions = () => {
    const actions = [];

    // GitHub settings button for suspend results
    if (result && !result.executedOnChannel) {
      actions.push(
        <Button key='github-settings' onClick={handleGoToGitHubSettings} variant='outlined' size='large'>
          {t('action.result.goToGitHubSettings' as any)}
        </Button>
      );
    }

    // Close button
    actions.push(
      <Button key='close' variant='contained' size='large' onClick={onCloseAction}>
        {t('action.result.closeButton' as any)}
      </Button>
    );

    return actions;
  };

  if (!result) {
    return <></>;
  }

  const alertMessage =
    actionType === IntegrationStatus.Suspended
      ? t('action.result.suspendNotExecutedOnChannel')
      : t('action.result.uninstallNotExecutedOnChannel');

  const errorDescriptionMessage =
    actionType === IntegrationStatus.Suspended
      ? t('action.result.suspendOnGitHubErrorDescription')
      : t('action.result.uninstalledOnGitHubErrorDescription');

  return (
    <Dialog
      open={open}
      onClose={onCloseAction}
      maxWidth='sm'
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2,
            boxShadow: (theme) => theme.shadows[10],
          },
        },
      }}
    >
      <DialogTitle sx={{ pb: 2 }}>
        <Typography variant='h5' component='div' sx={{ fontWeight: 600 }}>
          {t('action.title')}
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        <Stack spacing={3}>
          {/* Case 1: Not executed on GitHub because other workspaces use it */}
          {!result.executedOnChannel && result.otherWorkspaces.length > 0 ? (
            <Alert severity='warning'>
              <Typography sx={{ whiteSpace: 'pre-line' }}>{alertMessage}</Typography>
            </Alert>
          ) : /* Case 2: Not executed on GitHub, and no other workspaces (error) */
          !result.executedOnChannel && result.otherWorkspaces.length === 0 ? (
            <Alert severity='error'>
              <Typography sx={{ whiteSpace: 'pre-line' }}>{t('action.result.errorOnGitHub')}</Typography>
            </Alert>
          ) : null}

          {/* Details if other workspaces are involved */}
          {!result.executedOnChannel && result.otherWorkspaces.length > 0 ? (
            <Box>
              <Typography variant='body1' sx={{ mb: 2, whiteSpace: 'pre-line' }}>
                {t('action.result.withOtherWorkspaces' as any)}
              </Typography>
              <Stack spacing={1}>
                {result.otherWorkspaces.map((workspace) => (
                  <Link
                    key={workspace.id}
                    target='_blank'
                    href={`${paths.workspaceSelection}?workspaceName=${encodeURIComponent(workspace.name)}&workspaceId=${workspace.id}&returnTo=${encodeURIComponent(window.location.href)}`}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      color: 'primary.main',
                      textDecoration: 'none',
                      p: 1,
                      borderRadius: 1,
                      '&:hover': {
                        textDecoration: 'underline',
                        backgroundColor: 'action.hover',
                      },
                    }}
                  >
                    <Typography variant='body2' fontWeight={500}>
                      {workspace.name}
                    </Typography>
                    <LinkIcon size={16} style={{ marginLeft: '8px' }} />
                  </Link>
                ))}
              </Stack>
            </Box>
          ) : /* Description for error case */
          !result.executedOnChannel && result.otherWorkspaces.length === 0 ? (
            <Typography variant='body2' sx={{ whiteSpace: 'pre-line' }}>
              {errorDescriptionMessage}
            </Typography>
          ) : null}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>{renderActions()}</DialogActions>
    </Dialog>
  );
}
