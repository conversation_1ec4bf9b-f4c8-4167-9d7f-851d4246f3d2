// src/components/CountryAutocomplete.tsx
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import * as countries from 'i18n-iso-countries';
import { useLocale, useTranslations } from 'next-intl';
import * as React from 'react';

import { Language, languageAlias } from '@/lib/models/language';

interface CountryAutocompleteProps {
  value: string | null;
  onChange: (_value: string | null) => void;
  error?: boolean;
  helperText?: string;
}

const CountryAutocomplete: React.FC<CountryAutocompleteProps> = ({ value, onChange, error, helperText }) => {
  const t = useTranslations('settings.account.basicDetails');
  const locale = useLocale();
  const countryOptions = Object.entries(
    countries.getNames(languageAlias(locale as Language), { select: 'official' })
  ).map(([countryValue, countryLabel]) => ({ value: countryValue, label: countryLabel }));

  const selectedValue = countryOptions.find((option) => option.value === value) || null;

  return (
    <Autocomplete
      options={countryOptions}
      getOptionLabel={(option) => `${option.label} (${option.value})`}
      value={selectedValue}
      onChange={(_, newValue) => {
        onChange(newValue ? newValue.value : '');
      }}
      isOptionEqualToValue={(option, optionValue) => option.value === optionValue.value}
      renderInput={(params) => (
        <TextField
          {...params}
          label={t('countryLabel')}
          variant='outlined'
          fullWidth
          error={error}
          helperText={helperText}
        />
      )}
      renderOption={(props, option) => (
        <li {...props} key={option.value}>
          {option.label} ({option.value})
        </li>
      )}
      slotProps={{
        listbox: {
          style: { maxHeight: 300 },
        },
      }}
      selectOnFocus
      clearOnBlur
      handleHomeEndKeys
      openOnFocus
    />
  );
};

export default CountryAutocomplete;
