'use client';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useTranslations } from 'next-intl';
import * as React from 'react';

export function WelcomeStep(): React.JSX.Element {
  const t = useTranslations('onboarding');

  return (
    <Stack spacing={4} alignItems='center' textAlign='center'>
      <Typography variant='h4' component='h1'>
        {t('welcome.title')}
      </Typography>
      <Typography variant='body1' color='text.secondary' sx={{ maxWidth: 600 }}>
        {t('welcome.description')}
      </Typography>
      <Box
        component='img'
        src='/assets/onboarding-welcome.svg'
        alt={t('welcome.imageAlt')}
        sx={{
          maxWidth: '100%',
          height: 'auto',
          maxHeight: 240,
        }}
      />
    </Stack>
  );
}
