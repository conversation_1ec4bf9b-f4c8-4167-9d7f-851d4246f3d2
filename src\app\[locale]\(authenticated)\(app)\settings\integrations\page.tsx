import type { Metada<PERSON> } from 'next';
import * as React from 'react';

import { IntegrationsPageClient } from '@/components/settings/integrations/integrations-page-client';
import { config } from '@/config';

export const metadata = {
  title: `Integrations | Settings | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  return <IntegrationsPageClient />;
}
