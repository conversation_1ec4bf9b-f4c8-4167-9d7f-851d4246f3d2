import 'server-only';

import { redirect } from '@/i18n/navigation';
import { FirebaseServerApp, initializeApp, initializeServerApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getLocale } from 'next-intl/server';
import { cookies, headers } from 'next/headers';

import { paths } from '@/paths';
import { firebaseConfig } from '@/services/firebase/config';

import { logger } from '../../lib/logger/default-logger';

// Define a type for the server-side user
export type ServerUser = {
  uid: string;
  email: string | undefined;
  displayName: string | undefined;
  photoURL: string | undefined;
  emailVerified: boolean;
  phoneNumber: string | undefined;
  toJSON: () => {
    uid: string;
    email: string | undefined;
    displayName: string | undefined;
    photoURL: string | undefined;
    emailVerified: boolean;
    phoneNumber: string | undefined;
  };
};

// Returns an authenticated client SDK instance for use in Server Side Rendering
// and Static Site Generation
export async function getAuthenticatedAppForUser() {
  const authIdToken = (await cookies()).get('__session')?.value;

  var firebaseServerApp: FirebaseServerApp;

  try {
    firebaseServerApp = initializeServerApp(
      // https://github.com/firebase/firebase-js-sdk/issues/8863#issuecomment-2751401913
      firebaseConfig == undefined ? initializeApp() : initializeApp(firebaseConfig),
      {
        authIdToken,
      }
    );
  } catch (error) {
    logger.warn('Firebase server app initialization failed:', error);

    const locale = await getLocale();
    const headersList = await headers();
    const path = headersList.get('x-url') || headersList.get('referer') || headersList.get('origin');

    redirect({
      href: {
        pathname: paths.auth.signIn,
        query: {
          returnTo: path,
        },
      },
      locale,
    });

    throw error;
  }

  const auth = getAuth(firebaseServerApp);
  await auth.authStateReady();

  // TODO: review this strategy. Probably should have a simple User type for our own app and stop using firebase user type.
  return {
    firebaseServerApp,
    currentUser: auth.currentUser
      ? {
          uid: auth.currentUser.uid,
          email: auth.currentUser.email,
          displayName: auth.currentUser.displayName,
          photoURL: auth.currentUser.photoURL,
          emailVerified: auth.currentUser.emailVerified,
          phoneNumber: auth.currentUser.phoneNumber,
          // Convert to a format that can be serialized
          toJSON: () => ({
            uid: auth.currentUser?.uid,
            email: auth.currentUser?.email,
            displayName: auth.currentUser?.displayName,
            phoneNumber: auth.currentUser?.phoneNumber,
            photoURL: auth.currentUser?.photoURL,
            emailVerified: auth.currentUser?.emailVerified,
          }),
        }
      : undefined,
  };
}
