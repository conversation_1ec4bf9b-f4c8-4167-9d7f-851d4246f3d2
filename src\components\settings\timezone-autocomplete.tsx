import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import { useTranslations } from 'next-intl';
import React from 'react';

import { getDefaultTimezone, getTimezoneOptions } from '@/lib/models/timezone';

interface TimezoneAutocompleteProps {
  value: string;
  onChange: (_value: string) => void;
  error?: boolean;
  helperText?: string;
}

const TimezoneAutocomplete: React.FC<TimezoneAutocompleteProps> = ({ value, onChange, error, helperText }) => {
  const t = useTranslations('settings.account.basicDetails');
  const options = getTimezoneOptions();
  const defaultTz = getDefaultTimezone();
  const selected = options.find((option) => option.value === value) || defaultTz;

  return (
    <Autocomplete
      options={options}
      getOptionLabel={(option) => option.label}
      value={selected}
      onChange={(_, newValue) => {
        if (newValue) {
          onChange(newValue.value);
        }
      }}
      isOptionEqualToValue={(option, val) => option.value === val.value}
      renderInput={(params) => (
        <TextField
          {...params}
          label={t('timezone')}
          variant='outlined'
          fullWidth
          error={error}
          helperText={helperText}
        />
      )}
      renderOption={(props, option) => (
        <li {...props} key={option.value}>
          {option.label}
        </li>
      )}
      slotProps={{
        listbox: { style: { maxHeight: 300 } },
      }}
      selectOnFocus
      clearOnBlur
      handleHomeEndKeys
      openOnFocus
    />
  );
};

export default TimezoneAutocomplete;
