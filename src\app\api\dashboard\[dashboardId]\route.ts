/**
 * @swagger
 * /api/dashboard/{dashboardId}:
 *   get:
 *     summary: Get a specific dashboard by ID
 *     tags: [Dashboard]
 *     parameters:
 *       - in: path
 *         name: dashboardId
 *         required: true
 *         schema:
 *           type: string
 *         description: The dashboard ID
 *     responses:
 *       200:
 *         description: Dashboard details
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Dashboard not found or access denied
 *   put:
 *     summary: Update a dashboard
 *     tags: [Dashboard]
 *     parameters:
 *       - in: path
 *         name: dashboardId
 *         required: true
 *         schema:
 *           type: string
 *         description: The dashboard ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               layout:
 *                 type: object
 *               isDefault:
 *                 type: boolean
 *               isPublic:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Dashboard updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Dashboard not found or access denied
 *   delete:
 *     summary: Delete a dashboard
 *     tags: [Dashboard]
 *     parameters:
 *       - in: path
 *         name: dashboardId
 *         required: true
 *         schema:
 *           type: string
 *         description: The dashboard ID
 *     responses:
 *       200:
 *         description: Dashboard deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Dashboard not found or access denied
 */
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

// Schema for updating a dashboard
const updateDashboardSchema = z.object({
  name: z.string().min(1, 'Dashboard name is required').optional(),
  description: z.string().optional(),
  layout: z.record(z.any()).optional(),
  isDefault: z.boolean().optional(),
  isPublic: z.boolean().optional(),
});

interface RouteParams {
  params: Promise<{
    dashboardId: string;
  }>;
}

/**
 * GET /api/dashboard/[dashboardId]
 * Get a specific dashboard by ID
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const result = await getAuthenticatedAppForUser();
    if (!result?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { dashboardId } = await params;

    // Get the dashboard with workspace access check
    const dashboard = await db.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspace: {
          members: {
            some: {
              user: {
                email: result.currentUser.email,
              },
            },
          },
        },
      },
      include: {
        widgets: {
          orderBy: {
            createdAt: 'asc',
          },
        },
        createdBy: {
          select: {
            id: true,
            displayName: true,
            email: true,
          },
        },
        workspace: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!dashboard) {
      return NextResponse.json({ error: 'Dashboard not found or access denied' }, { status: 404 });
    }

    return NextResponse.json({ dashboard });
  } catch (error) {
    console.error('Error fetching dashboard:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PUT /api/dashboard/[dashboardId]
 * Update a dashboard
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const result = await getAuthenticatedAppForUser();
    if (!result?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { dashboardId } = await params;
    const body = await request.json();
    const validatedData = updateDashboardSchema.parse(body);

    // Check if dashboard exists and user has access
    const existingDashboard = await db.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspace: {
          members: {
            some: {
              user: {
                email: result.currentUser.email,
              },
            },
          },
        },
      },
    });

    if (!existingDashboard) {
      return NextResponse.json({ error: 'Dashboard not found or access denied' }, { status: 404 });
    }

    // If setting as default, unset other default dashboards in the same workspace
    if (validatedData.isDefault) {
      await db.dashboard.updateMany({
        where: {
          workspaceId: existingDashboard.workspaceId,
          isDefault: true,
          id: { not: dashboardId },
        },
        data: {
          isDefault: false,
        },
      });
    }

    // Update the dashboard
    const updatedDashboard = await db.dashboard.update({
      where: { id: dashboardId },
      data: validatedData,
      include: {
        widgets: {
          orderBy: {
            createdAt: 'asc',
          },
        },
        createdBy: {
          select: {
            id: true,
            displayName: true,
            email: true,
          },
        },
        workspace: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({ dashboard: updatedDashboard });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }

    console.error('Error updating dashboard:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE /api/dashboard/[dashboardId]
 * Delete a dashboard
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const result = await getAuthenticatedAppForUser();
    if (!result?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { dashboardId } = await params;

    // Check if dashboard exists and user has access
    const existingDashboard = await db.dashboard.findFirst({
      where: {
        id: dashboardId,
        workspace: {
          members: {
            some: {
              user: {
                email: result.currentUser.email,
              },
            },
          },
        },
      },
    });

    if (!existingDashboard) {
      return NextResponse.json({ error: 'Dashboard not found or access denied' }, { status: 404 });
    }

    // Delete the dashboard (widgets will be cascade deleted)
    await db.dashboard.delete({
      where: { id: dashboardId },
    });

    return NextResponse.json({ message: 'Dashboard deleted successfully' });
  } catch (error) {
    console.error('Error deleting dashboard:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
