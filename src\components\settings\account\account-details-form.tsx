'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import CircularProgress from '@mui/material/CircularProgress';
import Divider from '@mui/material/Divider';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import Grid from '@mui/material/Grid';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { User } from '@prisma/client';
import { matchIsValidTel, MuiTelInput } from 'mui-tel-input';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';

import CountryAutocomplete from '@/components/settings/country-autocomplete';
import TimezoneAutocomplete from '@/components/settings/timezone-autocomplete';
import { useCurrentUser } from '@/contexts/user-context';
import { useApiServices } from '@/hooks/use-api-services';

import { ProfilePicture } from './profile-picture';

interface FormValues {
  avatar?: string | null;
  displayName: string;
  email: string;
  phone?: string;
  country: string;
  timezone: string;
}

export function AccountDetailsForm(): React.JSX.Element {
  const t = useTranslations('settings.account.basicDetails');
  const { user, updateUser } = useCurrentUser();
  const { userApiService } = useApiServices();
  const [saving, setSaving] = React.useState(false);
  const [selectedAvatarFile, setSelectedAvatarFile] = React.useState<File | null>(null);
  const [formAlert, setFormAlert] = React.useState<{ type: 'success' | 'error'; message: string } | null>(null);

  // Validation schema
  const schema = React.useMemo(
    () =>
      z.object({
        avatar: z.string().nullable().optional(),
        displayName: z
          .string()
          .max(70, { message: t('maxNameSize', { size: '70' }) })
          .min(1, { message: t('displayNameRequired') }),
        email: z.string().email({ message: t('invalidEmail') }),
        phone: z
          .string()
          .optional()
          .refine((val) => !val || matchIsValidTel(val), { message: t('invalidPhone') }),
        country: z.string().min(1, { message: t('countryRequired') }),
        timezone: z.string().min(1, { message: t('timezoneRequired') }),
      }),
    [t]
  );

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isDirty, isValid },
  } = useForm<FormValues>({
    defaultValues: {
      avatar: user?.avatar ?? '',
      displayName: user?.displayName ?? '',
      email: user?.email ?? '',
      phone: user?.phone ?? '',
      country: user?.country ?? '',
      timezone: user?.timezone ?? '',
    },
    resolver: zodResolver(schema),
    mode: 'onBlur',
    reValidateMode: 'onChange',
  });

  // Ensure form default values are updated after user changes (e.g., after save)
  React.useEffect(() => {
    reset({
      avatar: user?.avatar ?? '',
      displayName: user?.displayName ?? '',
      email: user?.email ?? '',
      phone: user?.phone ?? '',
      country: user?.country ?? '',
      timezone: user?.timezone ?? '',
    });
  }, [user, reset]);

  const avatarUrl = watch('avatar');

  const handleFileSelectedInParent = (file: File | null) => {
    setSelectedAvatarFile(file);
    if (!file) {
      if (avatarUrl?.startsWith('data:image')) {
        setValue('avatar', user?.avatar || '', { shouldDirty: true });
      }
    }
  };

  const onSubmit = async (data: FormValues) => {
    setSaving(true);
    setFormAlert(null);
    try {
      let avatarPathToUpdate: string | undefined | null = data.avatar;

      // If a new file was selected, upload it first
      if (selectedAvatarFile) {
        try {
          const presignedData = await userApiService.createAvatarPresignedUrl(
            selectedAvatarFile.type,
            selectedAvatarFile.size
          );

          const xhr = new XMLHttpRequest();
          await new Promise((resolve, reject) => {
            xhr.addEventListener('load', () => {
              if (xhr.status === 200) {
                avatarPathToUpdate = presignedData.path;
                resolve(xhr.response);
              } else {
                reject(new Error('Upload failed'));
              }
            });
            xhr.addEventListener('error', () => reject(new Error('Upload error')));
            xhr.open('PUT', presignedData.url);
            xhr.setRequestHeader('Content-Type', selectedAvatarFile.type);
            xhr.setRequestHeader('Cache-Control', presignedData.cacheControl);
            xhr.send(selectedAvatarFile);
          });
        } catch (uploadError) {
          console.error('Avatar upload failed:', uploadError);
          setFormAlert({ type: 'error', message: t('avatarUploadError') });
          setSaving(false);
          return;
        }
      } else if (data.avatar === '' && user?.avatar) {
        // Avatar was explicitly removed (form value is empty, but user had an avatar)
        avatarPathToUpdate = '';
      } else if (data.avatar?.startsWith('data:image')) {
        // This means a preview was set, but no new *file* was selected for upload (e.g., user selected then unselected without saving)
        // Revert to original avatar path if current is a dataURL and no new file
        avatarPathToUpdate = user?.avatar;
      }

      const payload: Partial<User> = {
        displayName: data.displayName,
        country: data.country,
        phone: data.phone,
        timezone: data.timezone,
      };

      // Only include avatar in payload if it has actually changed (new path, or null for removal)
      // and it's not the temporary data URL.
      if (avatarPathToUpdate !== user?.avatar && !avatarPathToUpdate?.startsWith('data:image')) {
        payload.avatar = avatarPathToUpdate;
      }

      const hasProfileDataChanges =
        data.displayName !== user?.displayName ||
        data.country !== user?.country ||
        data.phone !== user?.phone ||
        data.timezone !== user?.timezone;

      const hasAvatarChange = payload.avatar !== undefined && payload.avatar !== user?.avatar;

      if (!hasProfileDataChanges && !hasAvatarChange) {
        setFormAlert({ type: 'success', message: t('noChanges') });
        setSaving(false);
        reset(data);
        return;
      }

      const updated = await userApiService.updateCurrentUser(payload);
      updateUser(updated);
      setSelectedAvatarFile(null);

      setFormAlert({ type: 'success', message: t('profileUpdatedSuccess') });
    } catch (err) {
      console.error('Failed to update user:', err);
      setFormAlert({ type: 'error', message: t('profileUpdateError') });
    } finally {
      setSaving(false);
    }
  };

  // Auto-hide form alert after 5 seconds
  React.useEffect(() => {
    if (formAlert) {
      const timer = setTimeout(() => setFormAlert(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [formAlert]);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card>
        {formAlert && (
          <Alert severity={formAlert.type} sx={{ m: 2, mb: 0 }} onClose={() => setFormAlert(null)}>
            {formAlert.message}
          </Alert>
        )}
        <Box sx={{ p: 3, pb: 1 }}>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={3}
            alignItems={{ xs: 'center', sm: 'flex-start' }}
            sx={{
              '& > .MuiStack-root:first-of-type': { order: { xs: 2, sm: 1 } },
              '& > .MuiStack-root:last-of-type': { order: { xs: 1, sm: 2 }, textAlign: { xs: 'center', sm: 'left' } },
            }}
          >
            <ProfilePicture
              setValueAction={setValue}
              formAvatarPath={avatarUrl}
              onFileSelectedAction={handleFileSelectedInParent}
              setFormAlertAction={setFormAlert}
            />
            <Stack spacing={0.5}>
              <Typography variant='h6' color='text.primary'>
                {t('title')}
              </Typography>
              <Typography variant='body2' color='text.secondary'>
                {t('subheader')}
              </Typography>
            </Stack>
          </Stack>
        </Box>
        <Divider />
        <CardContent>
          <Grid container spacing={3}>
            <Grid size={{ md: 6, xs: 12 }}>
              <Controller
                name='displayName'
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth required error={!!errors.displayName}>
                    <InputLabel>{t('displayName')}</InputLabel>
                    <OutlinedInput {...field} label={t('displayName')} />
                    {errors.displayName && <FormHelperText>{errors.displayName.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Grid>
            <Grid size={{ md: 6, xs: 12 }}>
              <Controller
                name='email'
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth required error={!!errors.email}>
                    <InputLabel>{t('email')}</InputLabel>
                    <OutlinedInput {...field} label={t('email')} readOnly disabled />
                  </FormControl>
                )}
              />
            </Grid>
            {/* Phone is not stored in user model */}
            <Grid size={{ md: 6, xs: 12 }}>
              <Controller
                name='phone'
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.phone}>
                    <MuiTelInput {...field} defaultCountry='BR' preferredCountries={['BR', 'US']} label={t('phone')} />
                    {errors.phone && <FormHelperText>{errors.phone.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Grid>
            <Grid size={{ md: 6, xs: 12 }}>
              <Controller
                name='country'
                control={control}
                render={({ field }) => (
                  <CountryAutocomplete
                    value={field.value}
                    onChange={(val) => field.onChange(val)}
                    error={!!errors.country}
                    helperText={errors.country?.message}
                  />
                )}
              />
            </Grid>
            <Grid size={{ md: 6, xs: 12 }}>
              <Controller
                name='timezone'
                control={control}
                render={({ field }) => (
                  <TimezoneAutocomplete
                    value={field.value}
                    onChange={(val) => field.onChange(val)}
                    error={!!errors.timezone}
                    helperText={errors.timezone?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        </CardContent>
        <Divider />
        <CardActions sx={{ justifyContent: 'flex-end' }}>
          <Button type='submit' variant='contained' disabled={!isDirty || saving || !isValid}>
            {saving ? <CircularProgress size={24} color='inherit' /> : t('saveChanges')}
          </Button>
        </CardActions>
      </Card>
    </form>
  );
}
