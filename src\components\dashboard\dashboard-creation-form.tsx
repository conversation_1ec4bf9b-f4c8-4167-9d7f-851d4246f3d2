'use client';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { PlusIcon } from '@phosphor-icons/react/dist/ssr/Plus';
import { useTranslations } from 'next-intl';
import * as React from 'react';

export interface DashboardCreationFormProps {
  workspaceId: string;
  onCreateDashboard: (data: { name: string; description?: string; workspaceId: string }) => Promise<void>;
  isLoading?: boolean;
}

export function DashboardCreationForm({ workspaceId, onCreateDashboard, isLoading = false }: DashboardCreationFormProps) {
  const t = useTranslations('dashboard');
  const [name, setName] = React.useState('');
  const [description, setDescription] = React.useState('');
  const [errors, setErrors] = React.useState<{ name?: string }>({});

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    // Validation
    const newErrors: { name?: string } = {};
    if (!name.trim()) {
      newErrors.name = t('create.nameRequired');
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      await onCreateDashboard({
        name: name.trim(),
        description: description.trim() || undefined,
        workspaceId,
      });
      
      // Reset form on success
      setName('');
      setDescription('');
      setErrors({});
    } catch (error) {
      // Error handling is done by the parent component
      console.error('Failed to create dashboard:', error);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh',
        p: 3,
      }}
    >
      <Card sx={{ maxWidth: 500, width: '100%' }}>
        <CardContent sx={{ p: 4 }}>
          <Stack spacing={3}>
            {/* Header */}
            <Stack spacing={1} alignItems="center">
              <Box
                sx={{
                  width: 64,
                  height: 64,
                  borderRadius: '50%',
                  bgcolor: 'primary.main',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 1,
                }}
              >
                <PlusIcon size={32} color="white" />
              </Box>
              <Typography variant="h4" textAlign="center">
                {t('create.title')}
              </Typography>
              <Typography variant="body1" color="text.secondary" textAlign="center">
                {t('create.description')}
              </Typography>
            </Stack>

            {/* Form */}
            <Box component="form" onSubmit={handleSubmit}>
              <Stack spacing={3}>
                <TextField
                  label={t('create.nameLabel')}
                  placeholder={t('create.namePlaceholder')}
                  value={name}
                  onChange={(e) => {
                    setName(e.target.value);
                    if (errors.name) {
                      setErrors((prev) => ({ ...prev, name: undefined }));
                    }
                  }}
                  error={!!errors.name}
                  helperText={errors.name}
                  required
                  fullWidth
                  disabled={isLoading}
                />

                <TextField
                  label={t('create.descriptionLabel')}
                  placeholder={t('create.descriptionPlaceholder')}
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  multiline
                  rows={3}
                  fullWidth
                  disabled={isLoading}
                />

                <Button
                  type="submit"
                  variant="contained"
                  size="large"
                  startIcon={<PlusIcon />}
                  disabled={isLoading}
                  fullWidth
                >
                  {isLoading ? t('create.creating') : t('create.createButton')}
                </Button>
              </Stack>
            </Box>
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );
}
