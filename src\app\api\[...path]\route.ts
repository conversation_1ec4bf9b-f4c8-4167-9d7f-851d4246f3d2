/* eslint-disable jsdoc/no-missing-syntax */
import 'server-only';

import { NextResponse } from 'next/server';

/**
 * Catch-all API route handler
 *
 * This file serves as a fallback for any API routes that don't exist in the API.
 * It returns a 404 response for all HTTP methods to ensure proper API error handling.
 *
 * Note: Specific API routes should be defined in their own route.ts files
 * (e.g., /api/hello/route.ts) which will take precedence over this catch-all handler.
 */

export async function GET(_: Request) {
  return NextResponse.json({ message: 'Invalid request' }, { status: 404 });
}

export async function POST(_: Request) {
  return NextResponse.json({ message: 'Invalid request' }, { status: 404 });
}

export async function PUT(_: Request) {
  return NextResponse.json({ message: 'Invalid request' }, { status: 404 });
}

export async function DELETE(_: Request) {
  return NextResponse.json({ message: 'Invalid request' }, { status: 404 });
}

export async function PATCH(_: Request) {
  return NextResponse.json({ message: 'Invalid request' }, { status: 404 });
}

export async function HEAD(_: Request) {
  return NextResponse.json({ message: 'Invalid request' }, { status: 404 });
}

export async function OPTIONS(_: Request) {
  return NextResponse.json({ message: 'Invalid request' }, { status: 404 });
}
