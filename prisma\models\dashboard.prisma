model Dashboard {
  id          String   @id @default(uuid())
  name        String
  description String?
  
  // Dashboard configuration
  layout      Json     // Stores grid layout configuration
  isD<PERSON><PERSON>   <PERSON>  @default(false)
  isPublic    Boolean  @default(false)
  
  // Ownership and workspace
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  createdById String
  createdBy   User     @relation("CreatedDashboards", fields: [createdById], references: [id])
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  widgets     DashboardWidget[]
  
  @@index([workspaceId])
  @@index([createdById])
}

model DashboardWidget {
  id          String   @id @default(uuid())
  
  // Widget configuration
  type        WidgetType
  title       String?
  config      Json     // Widget-specific configuration
  
  // Layout position
  x           Int
  y           Int
  width       Int
  height      Int
  
  // Relations
  dashboardId String
  dashboard   Dashboard @relation(fields: [dashboardId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([dashboardId])
}

enum WidgetType {
  METRIC_CARD
  SPARKLINE_CHART
  HEATMAP
  TEXT_INSIGHT
  COMMITS_CHART
  PULL_REQUESTS_CHART
  VELOCITY_CHART
  COPILOT_METRICS
}
