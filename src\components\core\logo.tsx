'use client';

import Box from '@mui/material/Box';
import { useColorScheme } from '@mui/material/styles';
import * as React from 'react';

import { NoSsr } from '@/components/core/no-ssr';

const HEIGHT = 60;
const WIDTH = 60;

type Color = 'dark' | 'light';

export interface LogoProps {
  color?: Color;
  emblem?: boolean;
  height?: number;
  width?: number;
}

export function Logo({ color = 'dark', emblem, height = HEIGHT, width = WIDTH }: LogoProps): React.JSX.Element {
  let url = '';

  if (emblem) {
    url = `/assets/gitpulse-emblem-${color}.svg`;
  } else {
    url = `/assets/gitpulse-logo-${color}.svg`;
  }

  return <Box alt='BMS Tech Pulse logo' component='img' height={height} src={url} width={width} />;
}

export interface DynamicLogoProps {
  emblem?: boolean;
  height?: number;
  width?: number;
}

export function DynamicLogo({ height = HEIGHT, width = WIDTH, ...props }: DynamicLogoProps): React.JSX.Element {
  const { colorScheme } = useColorScheme();

  return (
    <NoSsr fallback={<Box sx={{ height: `${height}px`, width: `${width}px` }} />}>
      <Logo color={colorScheme} height={height} width={width} {...props} />
    </NoSsr>
  );
}
