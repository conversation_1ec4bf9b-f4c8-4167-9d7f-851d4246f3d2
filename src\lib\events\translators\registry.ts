import { IntegrationChannel } from '@prisma/client';
import { Event } from '../types/event';

// Generic translator type that converts a source payload to an event
export type TranslatorFn<I, O extends Event> = (_input: I) => Promise<O[] | null>;

// Type for event constructor
type EventConstructor<T extends Event> = new (..._args: any[]) => T;

// Type-safe translator registry
class TranslatorRegistry {
  // Store translators by source type constructor and target type constructor
  private translators = new Map<IntegrationChannel, Map<Function, TranslatorFn<any, any>>>();

  /**
   * Register a translator function for specific source and target types
   * @param channel The integration channel
   * @param targetType The target event constructor type
   * @param translator The translator function
   */
  registry<I, O extends Event>(
    channel: IntegrationChannel,
    targetType: EventConstructor<O>,
    translator: TranslatorFn<I, O>
  ) {
    if (!this.translators.has(channel)) {
      this.translators.set(channel, new Map());
    }

    const typeTranslators = this.translators.get(channel)!;
    typeTranslators.set(targetType, translator);

    return this; // For method chaining
  }

  /**
   * Translate an input payload to an event
    channel: IntegrationChannel,
   * @param targetType The target event constructor
   * @param input The input payload to translate
   */
  async translate<I, O extends Event>(
    channel: IntegrationChannel,
    targetType: EventConstructor<O>,
    input: I
  ): Promise<O[] | null> {
    const typeTranslators = this.translators.get(channel);
    if (!typeTranslators) {
      throw new Error(`No translators registered for channel: ${channel}.`);
    }

    const translator = typeTranslators.get(targetType) as TranslatorFn<I, O>;
    if (!translator) {
      throw new Error(`No translator registered for channel ${channel} and target type ${targetType.name}`);
    }

    return translator(input);
  }

  /**
   * Check if a translator exists for given source and target types
   */
  hasTranslator(channel: IntegrationChannel, targetType: Function): boolean {
    const typeTranslators = this.translators.get(channel);

    return typeTranslators ? typeTranslators.has(targetType) : false;
  }
}

export const eventTranslatorRegistry = new TranslatorRegistry();
