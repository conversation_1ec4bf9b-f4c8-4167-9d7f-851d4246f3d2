import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import * as React from 'react';

import { config } from '@/config';

export const metadata = {
  title: `Billing & Plans | Settings | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const t = await getTranslations('settings');

  return (
    <Stack spacing={3}>
      <Card>
        <CardHeader title={t('billing.title')} subheader={t('billing.subheader')} />
        <Divider />
        <CardContent>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12 }}>
              <Typography variant='body1'>{t('billing.currentPlan')}</Typography>
              <Typography variant='h6' sx={{ mt: 1 }}>
                Free Plan
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Card>
        <CardHeader title={t('billing.availablePlans')} />
        <Divider />
        <CardContent>
          <Grid container spacing={3}>
            <Grid size={{ md: 4, xs: 12 }}>
              <Card variant='outlined' sx={{ height: '100%' }}>
                <CardHeader title='Free' />
                <Divider />
                <CardContent>
                  <Typography variant='h4' sx={{ mb: 2 }}>
                    $0
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    • Basic analytics
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    • Up to 5 users
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    • 1 repository
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid size={{ md: 4, xs: 12 }}>
              <Card variant='outlined' sx={{ height: '100%' }}>
                <CardHeader title='Pro' />
                <Divider />
                <CardContent>
                  <Typography variant='h4' sx={{ mb: 2 }}>
                    $29
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    • Advanced analytics
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    • Up to 20 users
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    • 10 repositories
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid size={{ md: 4, xs: 12 }}>
              <Card variant='outlined' sx={{ height: '100%' }}>
                <CardHeader title='Enterprise' />
                <Divider />
                <CardContent>
                  <Typography variant='h4' sx={{ mb: 2 }}>
                    $99
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    • Full analytics suite
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    • Unlimited users
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    • Unlimited repositories
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Stack>
  );
}
