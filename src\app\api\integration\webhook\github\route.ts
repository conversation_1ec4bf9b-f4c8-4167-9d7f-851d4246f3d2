import 'server-only';

import { logger } from '@/lib/logger/default-logger';
import { isUniqueConstraintError } from '@/lib/prisma/prisma';
import { registerWebhookHandlers } from '@/lib/webhooks/github';
import { db } from '@/services/db';
import { getInstance } from '@/services/github/app';
import { NextResponse } from 'next/server';

registerWebhookHandlers();

/**
 * @swagger
 * /api/integration/webhook/github:
 *   post:
 *     tags:
 *       - Integration
 *     summary: GitHub webhook endpoint
 *     description: Handles incoming GitHub webhook events
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 received:
 *                   type: boolean
 *       400:
 *         description: Missing required headers
 *       403:
 *         description: Invalid GitHub webhook signature
 *       500:
 *         description: Internal server error
 */
export async function POST(request: Request) {
  try {
    const receivedAt = new Date();

    // Extract GitHub webhook headers
    const delivery = request.headers.get('X-Github-Delivery');
    const event = request.headers.get('X-Github-Event');
    const hookId = request.headers.get('X-Github-Hook-Id');
    const hookInstallationTargetId = request.headers.get('X-Github-Hook-Installation-Target-Id');
    const hookInstallationTargetType = request.headers.get('X-Github-Hook-Installation-Target-Type');

    // Validate required headers
    if (!delivery || !event || !hookId) {
      return NextResponse.json(
        { message: 'Missing required GitHub webhook headers (X-Github-Delivery, X-Github-Event, X-Github-Hook-Id)' },
        { status: 400 }
      );
    }

    const secret = process.env.GITHUB_WEBHOOK_SECRET;
    if (!secret) {
      logger.error('GitHub webhook secret is not set on GITHUB_WEBHOOK_SECRET');

      return NextResponse.json({ message: 'Missing GitHub webhook secret' }, { status: 500 });
    }

    const signature256 = request.headers.get('X-Hub-Signature-256');

    if (!signature256) {
      return NextResponse.json(
        { message: 'Missing required GitHub webhook headers (X-Hub-Signature-256)' },
        { status: 400 }
      );
    }

    const body = await request.text();

    if (!(await getInstance().webhooks.verify(body, signature256))) {
      return NextResponse.json({ message: 'Invalid GitHub webhook signature' }, { status: 403 });
    }

    // Parse webhook payload
    const data = JSON.parse(body);

    const installationId = parseInt(data?.installation?.id);
    const installationAccountId = parseInt(data?.installation?.account?.id);
    const installationAccountLogin = data?.installation?.account?.login;

    // Store webhook in database
    try {
      await db.gitHubWebhook.create({
        data: {
          delivery,
          event,
          receivedAt,
          hookId,
          hookInstallationTargetType,
          hookInstallationTargetId,
          installationId,
          installationAccountId,
          installationAccountLogin,
          data,
        },
      });
    } catch (error) {
      if (!isUniqueConstraintError(error)) {
        throw error;
      }
    }

    // TODO: in the future we should save webhooks on pubsub, then store it there
    // And also we should process webhooks in another process
    await getInstance().webhooks.receive({
      id: delivery,
      name: event as any,
      payload: data,
    });

    logger.debug(`GitHub webhook stored successfully: ${event} - ${delivery}`);

    return NextResponse.json({ received: true }, { status: 200 });
  } catch (error) {
    console.error('Error processing GitHub webhook:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
