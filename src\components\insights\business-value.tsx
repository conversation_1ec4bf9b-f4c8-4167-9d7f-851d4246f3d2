'use client';

import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { ChartPieIcon } from '@phosphor-icons/react/dist/ssr/ChartPie';
import { TargetIcon } from '@phosphor-icons/react/dist/ssr/Target';
import type { ApexOptions } from 'apexcharts';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Chart } from '@/components/core/chart';

export function BusinessValue(): React.JSX.Element {
  const t = useTranslations('insights');
  const theme = useTheme();

  // Sample data for investment profile
  const investmentData = {
    categories: ['New Features', 'Tech Debt', 'Maintenance', 'Bug Fixes'],
    percentages: [45, 25, 15, 15],
  };

  // Sample data for dev ROI
  const devROIData = {
    initiatives: ['User Auth', 'Dashboard', 'API v2', 'Mobile App', 'Admin Panel'],
    throughput: [85, 65, 90, 75, 50],
    impact: [70, 90, 60, 95, 40],
  };

  // Chart options for investment profile chart
  const investmentChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
    },
    colors: [theme.palette.primary.main, theme.palette.warning.main, theme.palette.info.main, theme.palette.error.main],
    dataLabels: {
      enabled: true,
      formatter: (val) => `${String(val)}%`,
      style: {
        fontSize: '14px',
        fontWeight: 500,
        colors: [theme.palette.common.white],
      },
    },
    legend: {
      show: true,
      position: 'bottom',
      fontSize: '14px',
      fontWeight: 500,
      labels: {
        colors: theme.palette.text.secondary,
      },
    },
    plotOptions: {
      pie: {
        expandOnClick: false,
        donut: {
          size: '60%',
          labels: {
            show: true,
            name: {
              show: true,
              fontSize: '22px',
              fontWeight: 600,
              color: theme.palette.text.primary,
              offsetY: -10,
            },
            value: {
              show: true,
              fontSize: '16px',
              fontWeight: 400,
              color: theme.palette.text.secondary,
              offsetY: 16,
            },
            total: {
              show: true,
              label: 'Total',
              fontSize: '16px',
              fontWeight: 600,
              color: theme.palette.text.primary,
            },
          },
        },
      },
    },
    stroke: { width: 0 },
    theme: { mode: theme.palette.mode },
    tooltip: {
      fillSeriesColor: false,
    },
  };

  // Chart options for dev ROI chart
  const devROIChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    colors: [theme.palette.primary.main, theme.palette.success.main],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    legend: { show: true, position: 'top' },
    stroke: { curve: 'smooth', width: 3 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: devROIData.initiatives,
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      max: 100,
      labels: {
        formatter: (value) => `${value}`,
        style: { colors: theme.palette.text.secondary },
      },
    },
  };

  return (
    <Grid container spacing={3}>
      {/* Tech Debt vs Features Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.techDebtVsFeatures')} subheader={t('metrics.investmentProfile')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={300}
                options={{
                  ...investmentChartOptions,
                  labels: investmentData.categories,
                }}
                series={investmentData.percentages}
                type='donut'
                width='100%'
              />

              <Stack direction='row' spacing={2} alignItems='center'>
                <ChartPieIcon fontSize='var(--icon-fontSize-lg)' />
                <Stack sx={{ flex: 1 }}>
                  <Typography variant='body2'>25% of effort allocated to tech debt (recommended: 20-30%)</Typography>
                  <Typography variant='caption' color='text.secondary'>
                    Current ratio is within healthy range
                  </Typography>
                </Stack>
              </Stack>

              <Typography variant='body2' color='primary'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.defendRefactoringTime')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* Dev ROI Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.devROI')} subheader={t('metrics.throughputVsOKR')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={300}
                options={devROIChartOptions}
                series={[
                  { name: 'Development Throughput', data: devROIData.throughput },
                  { name: 'Business Impact', data: devROIData.impact },
                ]}
                type='radar'
                width='100%'
              />

              <Stack direction='row' spacing={2} alignItems='center'>
                <TargetIcon fontSize='var(--icon-fontSize-lg)' />
                <Stack sx={{ flex: 1 }}>
                  <Typography variant='body2'>Mobile App and Dashboard have highest business impact</Typography>
                  <Typography variant='caption' color='text.secondary'>
                    API v2 has highest throughput but lower impact
                  </Typography>
                </Stack>
              </Stack>

              <Typography variant='body2' color='primary'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.prioritiseRoadmap')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}
