'use client';

import { ActionConfirmationDialog } from '@/components/settings/integrations/github/action-confirmation-dialog';
import { ActionResultDialog } from '@/components/settings/integrations/github/action-result-dialog';
import { useWorkspace } from '@/contexts/workspace-context';
import { useApiServices } from '@/hooks/use-api-services';
import { useRouter } from '@/i18n/navigation';
import { logger } from '@/lib/logger/default-logger';
import { WorkspaceIntegrationWithChannels } from '@/lib/models/integration';
import { paths } from '@/paths';
import { IntegrationConfigComponentProps } from '@/services/integrations/integration';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Link from '@mui/material/Link';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Paper from '@mui/material/Paper';
import Snackbar from '@mui/material/Snackbar';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import {
  ArrowClockwiseIcon,
  DotsThreeIcon,
  LinkIcon,
  PauseIcon,
  PencilSimpleIcon,
  PlayIcon,
  PlugsIcon,
  PlusIcon,
} from '@phosphor-icons/react';
import { GitHubAccountType, IntegrationChannel, IntegrationStatus } from '@prisma/client';
import { useTranslations } from 'next-intl';
import * as React from 'react';

interface ActionsMenuProps {
  integrationId: string;
  entityStatus: string;
  onClickAction: (_id: string, _status: IntegrationStatus) => void;
  onEditAction: (_id: string) => void;
}

export function ActionsMenu({
  integrationId,
  entityStatus,
  onClickAction,
  onEditAction,
}: ActionsMenuProps): React.JSX.Element {
  const t = useTranslations('settings.integrations');
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const [isSmallScreen, setIsSmallScreen] = React.useState(false);

  React.useEffect(() => {
    // Function to update state based on window size
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 600);
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAction = (action: () => void) => {
    action();
    handleClose();
  };

  // Determine if we should show the compact or full button based on screen size
  return (
    <>
      {isSmallScreen ? (
        // Compact button for mobile
        <IconButton
          size='small'
          onClick={handleClick}
          aria-controls={open ? 'entity-actions-menu' : undefined}
          aria-haspopup='true'
          aria-expanded={open ? 'true' : undefined}
          aria-label={t('github.actions')}
        >
          <DotsThreeIcon />
        </IconButton>
      ) : (
        // Full button for desktop
        <Button
          variant='outlined'
          size='small'
          endIcon={<DotsThreeIcon />}
          onClick={handleClick}
          aria-controls={open ? 'entity-actions-menu' : undefined}
          aria-haspopup='true'
          aria-expanded={open ? 'true' : undefined}
        >
          {t('github.actions')}
        </Button>
      )}
      <Menu
        id='entity-actions-menu'
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={{
          list: {
            'aria-labelledby': 'entity-actions-button',
          },
        }}
      >
        <MenuItem onClick={() => handleAction(() => onEditAction(integrationId))}>
          <ListItemIcon>
            <PencilSimpleIcon size={20} />
          </ListItemIcon>
          <ListItemText>{t('github.editEntity')}</ListItemText>
        </MenuItem>

        <Divider />

        {entityStatus === 'Active' ? (
          <MenuItem
            onClick={() => handleAction(() => onClickAction(integrationId, IntegrationStatus.Suspended))}
            sx={{ color: 'warning.main' }}
          >
            <ListItemIcon>
              <PauseIcon size={20} />
            </ListItemIcon>
            <ListItemText>{t('github.suspendEntity')}</ListItemText>
          </MenuItem>
        ) : entityStatus === 'Suspended' ? (
          <MenuItem
            onClick={() => handleAction(() => onClickAction(integrationId, IntegrationStatus.Active))}
            sx={{ color: 'success.main' }}
          >
            <ListItemIcon>
              <PlayIcon size={20} />
            </ListItemIcon>
            <ListItemText>{t('github.resumeEntity' as any)}</ListItemText>
          </MenuItem>
        ) : null}

        <MenuItem
          onClick={() => handleAction(() => onClickAction(integrationId, IntegrationStatus.Uninstalled))}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <PlugsIcon size={20} />
          </ListItemIcon>
          <ListItemText>{t('github.uninstallEntity')}</ListItemText>
        </MenuItem>
      </Menu>
    </>
  );
}

export function GitHubConfig({
  workspaceIntegrations: initialWorkspaceIntegrations,
  integrationHandler,
}: IntegrationConfigComponentProps): React.JSX.Element {
  const t = useTranslations('settings.integrations');

  const router = useRouter();

  const [isLoading, setIsLoading] = React.useState(false);
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [workspaceIntegrations, setWorkspaceIntegrations] = React.useState<WorkspaceIntegrationWithChannels[]>(
    initialWorkspaceIntegrations || []
  );

  // Actions state
  const [currentActionIntegrationId, setCurrentActionIntegrationId] = React.useState<string>('');
  const [currentActionStatus, setCurrentActionStatus] = React.useState<IntegrationStatus | null>(null);
  const currentActionIntegration = React.useMemo(() => {
    if (!currentActionIntegrationId || !workspaceIntegrations) {
      return null;
    }
    return workspaceIntegrations.find((integration) => integration.id === currentActionIntegrationId);
  }, [currentActionIntegrationId, workspaceIntegrations]);
  const [isAddingInstallation, setIsAddingInstallation] = React.useState(false);

  // Dialog states
  const [confirmationDialogOpen, setConfirmationDialogOpen] = React.useState(false);
  const [resultDialogOpen, setResultDialogOpen] = React.useState(false);
  const [actionLoading, setActionLoading] = React.useState(false);

  // Snackbars
  const [snackbar, setSnackbar] = React.useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Results
  const [actionResult, setActionResult] = React.useState<{
    executedOnChannel: boolean;
    otherWorkspaces: Array<{
      id: string;
      name: string;
    }>;
  } | null>();

  // Hooks
  const { integrationApiService } = useApiServices();
  const { currentWorkspace } = useWorkspace();

  const renderAccountLink = (
    integration: {
      accountLogin: string;
      accountId?: number;
    },
    variant: 'body1' | 'body2' = 'body2',
    fontWeight?: string
  ) => {
    if (integration.accountLogin) {
      return (
        <Link
          href={`https://github.com/${integration.accountLogin}`}
          target='_blank'
          rel='noopener noreferrer'
          sx={{
            display: 'flex',
            alignItems: 'center',
            color: 'primary.main',
            textDecoration: 'none',
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          <Typography variant={variant} fontWeight={fontWeight}>
            {integration.accountLogin}
          </Typography>
          <LinkIcon size={16} style={{ marginLeft: '4px' }} />
        </Link>
      );
    }
    return <Typography variant={variant} fontWeight={fontWeight}>{`Account ${integration.accountId}`}</Typography>;
  };

  const renderStatusChip = (status: IntegrationStatus) => {
    let color: 'error' | 'info' | 'success' | 'warning' | 'primary' = 'info';
    let label = '';

    switch (status) {
      case IntegrationStatus.Active:
        color = 'success';
        label = t('github.statusActive');
        break;
      case IntegrationStatus.Suspended:
        color = 'warning';
        label = t('github.statusSuspended');
        break;
      case IntegrationStatus.Synchronizing:
        color = 'primary';
        label = t('github.statusSynchronizing');
        break;
      case IntegrationStatus.Uninstalled:
        color = 'error';
        label = t('github.statusUninstalled');

        break;
    }

    return <Chip label={label} size='small' color={color} clickable={false} onClick={() => {}} />;
  };

  const loadIntegrationDetails = React.useCallback(
    async (isRefresh: boolean) => {
      if (!currentWorkspace?.id) {
        logger.error('No workspace ID provided');
        return;
      }

      logger.debug('Loading GitHub integrations for workspace:', currentWorkspace.id);
      setIsLoading(true);
      try {
        const data = await integrationApiService.get(currentWorkspace.id, [IntegrationChannel.GitHub]);
        logger.debug('Loaded GitHub integrations:', data);
        setWorkspaceIntegrations(data || []);
      } catch (error) {
        if ('status' in (error as any) && (error as any).status === 404 && isRefresh) {
          logger.debug('No GitHub integrations found for workspace:', currentWorkspace.id);
          setWorkspaceIntegrations([]);
          return;
        }

        logger.error('Failed to load GitHub integrations', error);
        setSnackbar({
          open: true,
          message: t('github.errorLoadingDetails'),
          severity: 'error',
        });
      } finally {
        setIsLoading(false);
      }
    },
    [currentWorkspace?.id, integrationApiService, t]
  );

  const loadIntegrationDetailsRef = React.useRef(loadIntegrationDetails);
  React.useEffect(() => {
    loadIntegrationDetailsRef.current = loadIntegrationDetails;
  }, [loadIntegrationDetails]);

  React.useEffect(() => {
    if (!initialWorkspaceIntegrations || initialWorkspaceIntegrations.length == 0) {
      loadIntegrationDetailsRef.current(false);
    }
  }, [initialWorkspaceIntegrations]);

  const refreshDetails = async () => {
    setIsRefreshing(true);
    await loadIntegrationDetails(true);
    setIsRefreshing(false);
  };

  // Generic dialog handlers
  const handleOpenConfirmationDialog = (installationId: string, status: IntegrationStatus) => {
    setCurrentActionIntegrationId(installationId);
    setCurrentActionStatus(status);
    setConfirmationDialogOpen(true);
  };

  const handleCloseConfirmationDialog = () => {
    setConfirmationDialogOpen(false);
    setCurrentActionStatus(null);
    setCurrentActionIntegrationId('');
  };

  const handleCloseResultDialog = () => {
    setResultDialogOpen(false);
    setActionResult(null);
    setCurrentActionStatus(null);
    setCurrentActionIntegrationId('');
    refreshDetails();
  };

  const handleConfirmAction = async () => {
    if (!currentWorkspace?.id || !currentActionIntegrationId || !currentActionStatus) {
      logger.error('Missing required data for action confirmation');
      return;
    }

    setActionLoading(true);
    try {
      let result: {
        otherWorkspaces: {
          id: string;
          name: string;
        }[];
        executedOnChannel: boolean;
      } | null = null;

      if (currentActionStatus === IntegrationStatus.Suspended || currentActionStatus === IntegrationStatus.Active) {
        result = await integrationApiService.setStatus(
          currentWorkspace.id,
          currentActionIntegrationId,
          currentActionStatus
        );
      } else if (currentActionStatus === IntegrationStatus.Uninstalled) {
        result = await integrationApiService.uninstall(currentWorkspace.id, currentActionIntegrationId);
      } else {
        logger.error(`Unsupported action type: ${currentActionStatus}`);

        return;
      }

      if (result!.executedOnChannel) {
        setSnackbar({
          open: true,
          message: t('github.action.executedSuccessfully'),
          severity: 'success',
        });
        handleCloseConfirmationDialog();
        refreshDetails();
      } else {
        setActionResult(result);
        setConfirmationDialogOpen(false);
        setResultDialogOpen(true);
      }
    } catch (error) {
      logger.error(`Failed to ${currentActionStatus} installation`, error);
      setSnackbar({
        open: true,
        message: t('github.action.executedWithError'),
        severity: 'error',
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleEditInstallation = async (_installationId: string) => {
    setSnackbar({
      open: true,
      message: 'Edit functionality will be implemented in a future update',
      severity: 'info',
    });
  };

  const handleAddInstallation = React.useCallback(async () => {
    if (!currentWorkspace?.id) {
      logger.error('No workspace ID provided');
      return;
    }

    setIsAddingInstallation(true);
    try {
      await integrationHandler.onInstallAction({
        workspaceId: currentWorkspace?.id,
        integrationApiService,
        setIsLoading: setIsAddingInstallation,
      });
    } catch (error) {
      logger.error('Failed to add installation', error);
      setSnackbar({
        open: true,
        message: t('github.errorAddingInstallation'),
        severity: 'error',
      });
      setIsAddingInstallation(false);
    }
  }, [currentWorkspace?.id, integrationHandler, integrationApiService, t]);

  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false,
    });
  };

  if (!currentWorkspace || !currentWorkspace.id) {
    router.push({
      pathname: paths.workspaceSelection,
      query: {
        returnTo: window.location.href,
      },
    });

    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Stack spacing={3}>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {isLoading && workspaceIntegrations.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Card>
            <CardHeader
              title={t('github.installations')}
              subheader={
                workspaceIntegrations ? (
                  <Typography variant='subtitle2'>
                    {t('github.installedCount', { count: workspaceIntegrations.length || 0 })}
                  </Typography>
                ) : null
              }
              action={
                <Tooltip title={t('github.refreshIntegrations')}>
                  <IconButton onClick={refreshDetails} disabled={isRefreshing}>
                    {isRefreshing ? <CircularProgress size={24} /> : <ArrowClockwiseIcon size={24} />}
                  </IconButton>
                </Tooltip>
              }
            />
            <Divider />
            <CardContent>
              {!workspaceIntegrations || workspaceIntegrations.length === 0 ? (
                <Stack spacing={4} alignItems='center' sx={{ py: 4 }}>
                  <Typography variant='body1' color='text.secondary' align='center'>
                    {t('github.noIntegrations')}
                  </Typography>
                  <Button
                    variant='contained'
                    size='large'
                    startIcon={<PlusIcon weight='bold' />}
                    onClick={handleAddInstallation}
                    disabled={isAddingInstallation}
                  >
                    {isAddingInstallation ? (
                      <CircularProgress size={16} color='inherit' />
                    ) : (
                      t('github.addInstallation')
                    )}
                  </Button>
                </Stack>
              ) : (
                <>
                  {/* Desktop view - Table */}
                  <Box sx={{ display: { xs: 'none', md: 'block' }, mb: 3 }}>
                    <TableContainer component={Paper} sx={{ overflowX: 'auto' }}>
                      <Table size='small'>
                        <TableHead>
                          <TableRow>
                            <TableCell>{t('github.account')}</TableCell>
                            <TableCell>{t('github.type')}</TableCell>
                            <TableCell>{t('github.status')}</TableCell>
                            <TableCell>{t('github.connectedAt')}</TableCell>
                            <TableCell align='right'>{t('github.actions')}</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {workspaceIntegrations?.map((integration) => (
                            <TableRow key={integration.id}>
                              <TableCell>
                                <Stack direction='row' spacing={2} alignItems='center'>
                                  {renderAccountLink(integration.github!, 'body2')}
                                </Stack>
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={
                                    integration.github!.accountType === 'Organization'
                                      ? t('github.organization')
                                      : t('github.user')
                                  }
                                  size='small'
                                  color='primary'
                                  variant='outlined'
                                  clickable={false}
                                  onClick={() => {}}
                                />
                              </TableCell>
                              <TableCell>{renderStatusChip(integration.status)}</TableCell>
                              <TableCell>{integration.createdAt.toString()}</TableCell>
                              <TableCell align='right'>
                                <ActionsMenu
                                  integrationId={integration.id}
                                  entityStatus={integration.status}
                                  onClickAction={handleOpenConfirmationDialog}
                                  onEditAction={handleEditInstallation}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>

                  {/* Mobile view - Cards */}
                  <Box sx={{ display: { xs: 'flex', md: 'none' }, flexDirection: 'column', gap: 2, mb: 3 }}>
                    {workspaceIntegrations?.map((integration) => (
                      <Card key={integration.id} variant='outlined'>
                        <CardContent sx={{ p: 2 }}>
                          <Stack spacing={2}>
                            {/* Account info */}
                            <Stack direction='row' alignItems='center' justifyContent='space-between'>
                              <Box>
                                {renderAccountLink(integration.github!, 'body1', 'medium')}
                                <Typography variant='caption' color='text.secondary'>
                                  {integration.github!.accountType === 'Organization'
                                    ? t('github.organization')
                                    : t('github.user')}
                                </Typography>
                              </Box>
                              {renderStatusChip(integration.status)}
                            </Stack>

                            {/* Connected date */}
                            <Box>
                              <Typography variant='caption' color='text.secondary'>
                                {t('github.connectedAt')}:
                              </Typography>
                              <Typography variant='body2'>{integration.createdAt.toString()}</Typography>
                            </Box>

                            {/* Actions */}
                            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                              <ActionsMenu
                                integrationId={integration.id}
                                entityStatus={integration.status}
                                onClickAction={handleOpenConfirmationDialog}
                                onEditAction={handleEditInstallation}
                              />
                            </Box>
                          </Stack>
                        </CardContent>
                      </Card>
                    ))}
                  </Box>

                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      mt: 4,
                    }}
                  >
                    <Button
                      variant='contained'
                      startIcon={isAddingInstallation ? <></> : <PlusIcon weight='bold' />}
                      onClick={handleAddInstallation}
                      disabled={isAddingInstallation}
                      size='medium'
                    >
                      {isAddingInstallation ? (
                        <CircularProgress size={16} color='inherit' />
                      ) : (
                        t('github.addInstallation')
                      )}
                    </Button>
                  </Box>
                </>
              )}
            </CardContent>
          </Card>

          <ActionConfirmationDialog
            open={confirmationDialogOpen}
            onCloseAction={handleCloseConfirmationDialog}
            onConfirmAction={handleConfirmAction}
            actionType={currentActionStatus || undefined}
            accountLogin={currentActionIntegration?.github?.accountLogin || undefined}
            loading={actionLoading}
          />

          <ActionResultDialog
            open={resultDialogOpen}
            onCloseAction={handleCloseResultDialog}
            result={actionResult}
            actionType={currentActionStatus}
            githubAccount={
              currentActionIntegration
                ? {
                    accountLogin: currentActionIntegration.github!.accountLogin,
                    accountType: currentActionIntegration.github!.accountType as GitHubAccountType,
                    installationId: currentActionIntegration.github!.installationId.toString(),
                  }
                : undefined
            }
          />
        </>
      )}
    </Stack>
  );
}
