import { resolve } from 'node:path';

import eslint from '@eslint/js';
import nextPlugin from '@next/eslint-plugin-next';
import tseslint from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import prettierConfig from 'eslint-config-prettier';
import importPlugin from 'eslint-plugin-import';
import jsdocPlugin from 'eslint-plugin-jsdoc';
import reactPlugin from 'eslint-plugin-react';
import reactHooksPlugin from 'eslint-plugin-react-hooks';
import tailwindcssPlugin from 'eslint-plugin-tailwindcss';
import globals from 'globals';

const project = resolve('./tsconfig.json');

export default [
  // Ignore patterns (replaces .eslintignore)
  {
    ignores: [
      'node_modules/**',
      '.next/**',
      'out/**',
      '.husky/**',
      'coverage/**',
      'stories/**',
      'storybook-static/**',
      '*.log',
      'playwright-report/**',
      '.nyc_output/**',
      'test-results/**',
      'junit.xml',
      'docs/**',
      'jest.config.js',
      'jest.setup.js',
      'local.mjs',
    ],
  },

  // Base ESLint recommended rules
  eslint.configs.recommended,

  // TypeScript configuration
  {
    files: ['**/*.{ts,tsx}'],
    plugins: {
      '@typescript-eslint': tseslint,
    },
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        project,
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    rules: {
      // TypeScript specific rules
      '@typescript-eslint/no-unused-vars': [
        'warn',
        {
          ignoreRestSiblings: true,
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
        },
      ],
      '@typescript-eslint/no-empty-interface': [
        'error',
        {
          allowSingleExtends: true,
        },
      ],
      '@typescript-eslint/no-shadow': [
        'error',
        {
          ignoreOnInitialization: true,
        },
      ],
      '@typescript-eslint/dot-notation': 'off', // paths are used with a dot notation
      '@typescript-eslint/no-misused-promises': 'off', // onClick with async fails
      '@typescript-eslint/no-non-null-assertion': 'off', // sometimes compiler is unable to detect
      '@typescript-eslint/no-unnecessary-condition': 'off', // remove when no static data is used
      '@typescript-eslint/require-await': 'off', // Server Actions require async flag always
      '@typescript-eslint/prefer-nullish-coalescing': 'off', // personal style
      '@typescript-eslint/restrict-template-expressions': [
        'error',
        {
          allowNumber: true,
        },
      ],
    },
  },

  // React configuration
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      react: reactPlugin,
      'react-hooks': reactHooksPlugin,
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
    rules: {
      // React specific rules
      'react/jsx-uses-react': 'off',
      'react/react-in-jsx-scope': 'off',
      'react/jsx-fragments': 'off', // personal style
      'react/prop-types': 'off', // TypeScript is used for type checking
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',
      // Avoid hardcoded labels, making sure they are translated
      // TODO: active this after translating all components
      //'react/jsx-no-literals': [
      //  'error',
      //  {
      //    message: 'Please use a translation function instead of hardcoding labels.',
      //  }
      //],
      // Consistently import navigation APIs from `@/i18n/navigation`
      'no-restricted-imports': [
        'error',
        // TODO: enable this after compatibility with Next.js is fixed
        // https://github.com/amannn/next-intl/issues/1271#issuecomment-2852253611
        //{
        //  name: 'next/link',
        //  message: 'Please import from `@/i18n/navigation` instead.',
        //},
        {
          name: 'next/navigation',
          importNames: ['redirect', 'permanentRedirect', 'useRouter', 'usePathname'],
          message: 'Please import from `@/i18n/navigation` instead.',
        },
      ],
    },
  },

  // Next.js configuration
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      '@next/next': nextPlugin,
    },
    rules: {
      '@next/next/no-img-element': 'off', // Temporarily disabled
      '@next/next/no-html-link-for-pages': 'error',
    },
  },

  // Tailwind CSS configuration
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      tailwindcss: tailwindcssPlugin,
    },
    rules: {
      'tailwindcss/classnames-order': 'off', // disabled for now
      'tailwindcss/no-custom-classname': 'warn',
    },
  },

  // Import plugin configuration
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      import: importPlugin,
    },
    // Simplify the settings to avoid potential compatibility issues
    settings: {
      'import/resolver': {
        node: {
          extensions: ['.js', '.jsx', '.ts', '.tsx'],
        },
      },
    },
    rules: {
      'import/newline-after-import': 'error',
      'import/no-default-export': 'off', // Next.js components must be exported as default
      'import/no-extraneous-dependencies': 'off', // conflict with sort-imports plugin
      'import/order': 'off', // using custom sort plugin
    },
  },

  // Browser environment configuration
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.jest,
      },
    },
  },

  // Other general rules
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    rules: {
      'no-nested-ternary': 'off', // personal style
      'no-redeclare': 'off', // conflict with TypeScript function overloads
      'no-unused-vars': [
        'warn',
        {
          ignoreRestSiblings: true,
          argsIgnorePattern: '^_',
          varsIgnorePattern: '(^_)|(^[A-Z])', // Ignore _ prefixed vars and uppercase (exports)
          caughtErrorsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
        },
      ],
    },
  },

  // Force the setting of a swagger description on each api endpoint
  {
    files: ['src/app/api/**/route.ts'],
    ignores: ['**/__tests__/**', '**/*.test.ts', '**/*.spec.ts'],
    plugins: {
      jsdoc: jsdocPlugin,
    },
    rules: {
      'jsdoc/no-missing-syntax': [
        'error',
        {
          contexts: [
            {
              comment: 'JsdocBlock:has(JsdocTag[tag=swagger])',
              context: 'any',
              message:
                '@swagger documentation is required on each API. Check this out for syntax info: https://github.com/jellydn/next-swagger-doc',
            },
          ],
        },
      ],
    },
  },

  // Prettier config (must be last to override other formatting rules)
  prettierConfig,
];
