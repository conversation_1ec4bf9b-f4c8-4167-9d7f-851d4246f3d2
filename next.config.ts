import { NextConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin({
  experimental: {
    createMessagesDeclaration: ['./messages/en-US.json', './messages/pt-BR.json', './messages/es.json'],
  },
});

const config: NextConfig = {
  // Firebase App Hosting is designed to work efficiently with Next.js's standalone output
  output: 'standalone',

  transpilePackages: ['mui-tel-input'],

  // Remove x-powered-by header since it exposes Next usage
  poweredByHeader: false,

  // Disable trailing slash to avoid having `/` at the end of paths
  trailingSlash: false,

  // Disable eslint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Make sure images have the correct path
  images: {
    loader: 'akamai',
    path: '/',
  },

  logging: {
    incomingRequests: {
      // Only log requests that start with /api
      ignore: [/^(?!\/api).+$/],
    },
  },
};

export default withNextIntl(config);
