import { Integration, IntegrationHandler } from './integration';

// Registry to store all available integrations
class IntegrationsRegistry {
  private handlers: Record<string, IntegrationHandler> = {};

  // Register a new integration handler
  register(handler: IntegrationHandler): void {
    const integration = handler.getIntegration();
    this.handlers[integration.id] = handler;
  }

  // Get a specific integration handler by ID
  getHandler(id: string): IntegrationHandler | undefined {
    return this.handlers[id];
  }

  // Get all registered integrations
  getAllIntegrations(): Integration[] {
    return Object.values(this.handlers).map((handler) => handler.getIntegration());
  }

  // Get all registered handlers
  getAllHandlers(): IntegrationHandler[] {
    return Object.values(this.handlers);
  }
}

// Create and export a singleton instance
export const integrationsRegistry = new IntegrationsRegistry();
