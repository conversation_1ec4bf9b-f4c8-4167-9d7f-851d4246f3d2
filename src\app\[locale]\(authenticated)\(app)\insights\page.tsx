import { redirect } from '@/i18n/navigation';
import type { Metadata } from 'next';
import { getLocale } from 'next-intl/server';

import { config } from '@/config';
import { paths } from '@/paths';

export const metadata = {
  title: `Engineering Insights | ${config.site.name}`,
} satisfies Metadata;

export default async function Page() {
  const locale = await getLocale();

  redirect({
    href: paths.insights.delivery,
    locale: locale,
  });

  return null;
}
