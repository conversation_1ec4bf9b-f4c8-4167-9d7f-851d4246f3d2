import { GitCommit, GitPullRequest, GitRepository, IntegrationChannel, IntegrationProfile } from '@prisma/client';
import { Event, EventType } from './event';

export enum GitEventAction {
  PullRequest = 'pullRequest',
  Repository = 'repository',
  Push = 'push',
}

// Base git event interface
export class GitEvent implements Event {
  type: EventType;
  channel: IntegrationChannel;
  id: string;
  integrationId?: string;
  workspaceId?: string;
  gitEventAction: GitEventAction;
  timestamp: Date;
  author?: Partial<IntegrationProfile>;
  rawPayload?: Record<string, any>;

  constructor(data: {
    id: string;
    channel: IntegrationChannel;
    timestamp: Date;
    gitEventAction: GitEventAction;
    author?: Partial<IntegrationProfile>;
    workspaceId?: string;
    integrationId?: string;
    rawPayload?: Record<string, any>;
  }) {
    this.type = EventType.Git;
    this.channel = data.channel;
    this.id = data.id;
    this.integrationId = data.integrationId;
    this.workspaceId = data.workspaceId;

    this.timestamp = data.timestamp;
    this.gitEventAction = data.gitEventAction;
    this.author = data.author;
    this.rawPayload = data.rawPayload;
  }
}

export enum GitRepositoryEventAction {
  Created = 'created',
  Deleted = 'deleted',
  Archived = 'archived',
  Unarchived = 'unarchived',
}

export class GitRepositoryEvent extends GitEvent {
  repository: Partial<GitRepository>;
  repositoryEventAction: GitRepositoryEventAction;

  constructor(data: {
    id: string;
    channel: IntegrationChannel;
    integrationId?: string;
    workspaceId?: string;
    timestamp: Date;
    repository: Partial<GitRepository>;
    repositoryEventAction: GitRepositoryEventAction;
    author: Partial<IntegrationProfile>;
    rawPayload?: Record<string, any>;
  }) {
    super({
      id: data.id,
      channel: data.channel,
      integrationId: data.integrationId,
      workspaceId: data.workspaceId,
      timestamp: data.timestamp,
      gitEventAction: GitEventAction.Repository,
      author: data.author,
      rawPayload: data.rawPayload,
    });
    this.repositoryEventAction = data.repositoryEventAction;
    this.repository = data.repository;
  }
}

export enum GitPullRequestEventAction {
  Opened = 'opened',
  Closed = 'closed',
  Merged = 'merged',
  Reopened = 'reopened',
  Edited = 'edited',
}

export class GitPullRequestEvent extends GitEvent {
  pullRequest: Partial<GitPullRequest>;
  repository: Partial<GitRepository>;
  pullRequestEventAction: GitPullRequestEventAction;
  pullRequestUser: Partial<IntegrationProfile>;

  constructor(data: {
    id: string;
    channel: IntegrationChannel;
    integrationId?: string;
    workspaceId?: string;
    timestamp: Date;
    pullRequestEventAction: GitPullRequestEventAction;
    pullRequest: Partial<GitPullRequest>;
    repository: Partial<GitRepository>;
    author: Partial<IntegrationProfile>;
    rawPayload?: Record<string, any>;
    pullRequestUser: Partial<IntegrationProfile>;
  }) {
    super({
      gitEventAction: GitEventAction.PullRequest,
      channel: data.channel,
      id: data.id,
      integrationId: data.integrationId,
      workspaceId: data.workspaceId,
      timestamp: data.timestamp,
      author: data.author,
      rawPayload: data.rawPayload,
    });

    this.pullRequestEventAction = data.pullRequestEventAction;
    this.pullRequest = data.pullRequest;
    this.repository = data.repository;
    this.author = data.author;
    this.pullRequestUser = data.pullRequestUser;
  }
}

export class GitCommitEvent extends GitEvent {
  commit: Partial<GitCommit>;
  repository: Partial<GitRepository>;
  committer?: Partial<IntegrationProfile>;

  constructor(data: {
    // For commits, id is the commit SHA
    id: string;
    channel: IntegrationChannel;
    integrationId?: string;
    workspaceId?: string;
    timestamp: Date;
    commit: Partial<GitCommit>;
    repository: Partial<GitRepository>;
    author: Partial<IntegrationProfile>;
    committer?: Partial<IntegrationProfile>;
    rawPayload?: Record<string, any>;
  }) {
    super({
      gitEventAction: GitEventAction.Push,
      channel: data.channel,
      id: data.id,
      integrationId: data.integrationId,
      workspaceId: data.workspaceId,
      timestamp: data.timestamp,
      author: data.author,
      rawPayload: data.rawPayload,
    });

    this.commit = data.commit;
    this.repository = data.repository;
    this.committer = data.committer;
  }
}

export class GitPushEvent extends GitEvent {
  repository: Partial<GitRepository>;
  commits: Partial<GitCommitEvent>[];
  branch: string;
  forcePush?: boolean;

  constructor(data: {
    id: string;
    channel: IntegrationChannel;
    integrationId?: string;
    workspaceId?: string;
    timestamp: Date;
    repository: Partial<GitRepository>;
    author: Partial<IntegrationProfile>;
    branch: string;
    commits: Partial<GitCommitEvent>[];
    forcePush?: boolean;
    rawPayload?: Record<string, any>;
  }) {
    super({
      gitEventAction: GitEventAction.Push,
      id: data.id,
      channel: data.channel,
      integrationId: data.integrationId,
      workspaceId: data.workspaceId,
      timestamp: data.timestamp,
      author: data.author,
      rawPayload: data.rawPayload,
    });

    this.repository = data.repository;
    this.commits = data.commits;
    this.branch = data.branch;
    this.forcePush = data.forcePush;
  }
}
