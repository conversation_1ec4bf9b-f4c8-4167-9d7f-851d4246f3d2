'use client';

import {
  closestCenter,
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { PlusIcon } from '@phosphor-icons/react/dist/ssr/Plus';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { DraggableWidget } from './draggable-widget';
import { useDashboardState } from './hooks/use-dashboard-state';
import { WidgetLibraryModal } from './widget-library-modal';

export interface DashboardBuilderProps {
  dashboardId?: string;
  workspaceId: string;
  isEditable?: boolean;
}

export function DashboardBuilder({ dashboardId, workspaceId, isEditable = true }: DashboardBuilderProps) {
  const t = useTranslations('dashboard');
  const [activeId, setActiveId] = React.useState<string | null>(null);
  const [isWidgetLibraryOpen, setIsWidgetLibraryOpen] = React.useState(false);

  const { dashboard, widgets, isLoading, error, createDashboard, addWidget, updateWidgetPositions, removeWidget } =
    useDashboardState(dashboardId, workspaceId);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = widgets.findIndex((widget) => widget.id === active.id);
      const newIndex = widgets.findIndex((widget) => widget.id === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newWidgets = arrayMove(widgets, oldIndex, newIndex);

        // Update positions based on new order
        const updatedWidgets = newWidgets.map((widget, index) => ({
          id: widget.id,
          x: (index % 4) * 3, // 4 columns, each widget takes 3 grid units
          y: Math.floor(index / 4) * 2, // 2 rows height per widget
          width: widget.width,
          height: widget.height,
        }));

        updateWidgetPositions(updatedWidgets);
      }
    }

    setActiveId(null);
  };

  const handleAddWidget = async (widgetType: string, config: Record<string, any>) => {
    if (!dashboard) return;

    // Calculate position for new widget
    const nextPosition = {
      x: (widgets.length % 4) * 3,
      y: Math.floor(widgets.length / 4) * 2,
      width: 3,
      height: 2,
    };

    await addWidget({
      type: widgetType as any,
      title: config.title,
      config,
      ...nextPosition,
    });

    setIsWidgetLibraryOpen(false);
  };

  const handleRemoveWidget = async (widgetId: string) => {
    await removeWidget(widgetId);
  };

  const handleCreateDashboard = async (data: { name: string; description?: string; workspaceId: string }) => {
    await createDashboard(data);
  };

  if (isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>{t('loading')}</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color='error'>
          {t('error')}: {error}
        </Typography>
      </Box>
    );
  }

  if (!dashboard) {
    // Import the component dynamically to avoid circular dependency issues
    const DashboardCreationForm = React.lazy(() =>
      import('./dashboard-creation-form').then((module) => ({ default: module.DashboardCreationForm }))
    );

    return (
      <React.Suspense
        fallback={
          <Box sx={{ p: 3 }}>
            <Typography>{t('loading')}</Typography>
          </Box>
        }
      >
        <DashboardCreationForm
          workspaceId={workspaceId}
          onCreateDashboard={handleCreateDashboard}
          isLoading={isLoading}
        />
      </React.Suspense>
    );
  }

  const activeWidget = activeId ? widgets.find((widget) => widget.id === activeId) : null;

  return (
    <Box sx={{ p: 3 }}>
      <Stack spacing={3}>
        {/* Dashboard Header */}
        <Stack direction='row' justifyContent='space-between' alignItems='center'>
          <Stack spacing={1}>
            <Typography variant='h4'>{dashboard.name}</Typography>
            {dashboard.description && (
              <Typography variant='body2' color='text.secondary'>
                {dashboard.description}
              </Typography>
            )}
          </Stack>

          {isEditable && (
            <Button variant='contained' startIcon={<PlusIcon />} onClick={() => setIsWidgetLibraryOpen(true)}>
              {t('addWidget')}
            </Button>
          )}
        </Stack>

        {/* Dashboard Grid */}
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <SortableContext items={widgets.map((w) => w.id)}>
            <Grid container spacing={3}>
              {widgets.map((widget) => (
                <Grid key={widget.id} size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                  <DraggableWidget
                    widget={widget}
                    isEditable={isEditable}
                    onRemove={() => handleRemoveWidget(widget.id)}
                  />
                </Grid>
              ))}

              {widgets.length === 0 && (
                <Grid size={{ xs: 12 }}>
                  <Paper
                    sx={{
                      p: 6,
                      textAlign: 'center',
                      border: '2px dashed',
                      borderColor: 'divider',
                      bgcolor: 'background.default',
                    }}
                  >
                    <Stack spacing={2} alignItems='center'>
                      <Typography variant='h6' color='text.secondary'>
                        {t('emptyDashboard')}
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        {t('emptyDashboardDescription')}
                      </Typography>
                      {isEditable && (
                        <Button
                          variant='outlined'
                          startIcon={<PlusIcon />}
                          onClick={() => setIsWidgetLibraryOpen(true)}
                        >
                          {t('addFirstWidget')}
                        </Button>
                      )}
                    </Stack>
                  </Paper>
                </Grid>
              )}
            </Grid>
          </SortableContext>

          <DragOverlay>
            {activeWidget ? <DraggableWidget widget={activeWidget} isEditable={false} isDragging /> : null}
          </DragOverlay>
        </DndContext>

        {/* Widget Library Modal */}
        <WidgetLibraryModal
          open={isWidgetLibraryOpen}
          onClose={() => setIsWidgetLibraryOpen(false)}
          onAddWidget={handleAddWidget}
        />
      </Stack>
    </Box>
  );
}
