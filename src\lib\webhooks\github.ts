import { eventProcessorRegistry } from '@/lib/events/processors';
import { eventTranslatorRegistry } from '@/lib/events/translators';
import { GitPullRequestEvent, GitPushEvent, GitRepositoryEvent } from '@/lib/events/types/git';
import { logger } from '@/lib/logger/default-logger';
import { getInstance } from '@/services/github/app';
import { IntegrationChannel } from '@prisma/client';

export function registerWebhookHandlers() {
  // Handle repository events (created, deleted, archived, unarchived)
  getInstance().webhooks.on(['repository'], async (event) => {
    try {
      // Use the translator registry to translate the event using types
      const translatedEvents = await eventTranslatorRegistry.translate(
        IntegrationChannel.GitHub,
        GitRepositoryEvent,
        event
      );

      if (!translatedEvents?.length) {
        logger.debug('Skipping GitHub repository event', { eventId: event.id });
        return;
      }

      for (const translatedEvent of translatedEvents) {
        await eventProcessorRegistry.process(translatedEvent);
      }
    } catch (error) {
      logger.error('Error processing GitHub repository event', { eventId: event.id }, error);
    }
  });

  // Handle pull request events (opened, closed, merged, reopened)
  getInstance().webhooks.on(['pull_request'], async (event) => {
    try {
      // Use the translator registry to translate the event using types
      const translatedEvents = await eventTranslatorRegistry.translate(
        IntegrationChannel.GitHub,
        GitPullRequestEvent,
        event
      );

      if (!translatedEvents?.length) {
        logger.debug('Skipping GitHub pull request event', { eventId: event.id });
        return;
      }

      for (const translatedEvent of translatedEvents) {
        await eventProcessorRegistry.process(translatedEvent);
      }
    } catch (error) {
      logger.error('Error processing GitHub pull request event', { eventId: event.id }, error);
    }
  });

  // Handle push events
  getInstance().webhooks.on(['push'], async (event) => {
    try {
      // Use the translator registry to translate the event using types
      const translatedEvents = await eventTranslatorRegistry.translate(IntegrationChannel.GitHub, GitPushEvent, event);

      if (!translatedEvents?.length) {
        logger.debug('Skipping GitHub push event', { eventId: event.id });
        return;
      }

      for (const translatedEvent of translatedEvents) {
        await eventProcessorRegistry.process(translatedEvent);
      }
    } catch (error) {
      logger.error('Error processing GitHub push event', { eventId: event.id }, error);
    }
  });
}
