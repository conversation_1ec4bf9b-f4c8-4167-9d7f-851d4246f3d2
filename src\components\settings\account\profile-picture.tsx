'use client';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { CameraIcon } from '@phosphor-icons/react/dist/ssr/Camera';
import { XIcon } from '@phosphor-icons/react/dist/ssr/X';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { UseFormSetValue } from 'react-hook-form';

import { UserAvatar } from '@/components/core/user-avatar';
import { useCurrentUser } from '@/contexts/user-context';

interface ProfilePictureProps {
  setValueAction: UseFormSetValue<any>;
  formAvatarPath?: string | null;
  onFileSelectedAction: (_file: File | null) => void;
  setFormAlertAction: (_alert: { type: 'success' | 'error'; message: string } | null) => void;
}

export function ProfilePicture({
  setValueAction,
  formAvatarPath,
  onFileSelectedAction,
  setFormAlertAction,
}: ProfilePictureProps): React.JSX.Element {
  const t = useTranslations('settings.account.profilePicture');
  const { user } = useCurrentUser();
  const [hover, setHover] = React.useState(false);
  const [localPreviewUrl, setLocalPreviewUrl] = React.useState<string | null>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const avatarRef = React.useRef<HTMLDivElement>(null);

  // Clear local preview when form avatar path changes (e.g., after save)
  React.useEffect(() => {
    if (formAvatarPath && !formAvatarPath.startsWith('data:image') && localPreviewUrl) {
      setLocalPreviewUrl(null);
    }
  }, [formAvatarPath, localPreviewUrl]);

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const validateFile = (file: File): string | null => {
    // Check file type
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
    if (!allowedTypes.includes(file.type)) {
      return t('invalidFileType');
    }

    // Check file size (2MB limit)
    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      return t('fileTooLarge', { size: '2' });
    }

    return null;
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    const validationError = validateFile(file);
    if (validationError) {
      setFormAlertAction({ type: 'error', message: validationError });
      onFileSelectedAction(null);
      setLocalPreviewUrl(null);
      return;
    }

    setFormAlertAction(null);

    const reader = new FileReader();
    reader.onloadend = () => {
      setLocalPreviewUrl(reader.result as string);
      setValueAction('avatar', reader.result as string, { shouldDirty: true });
      onFileSelectedAction(file);
    };

    reader.onerror = () => {
      setFormAlertAction({ type: 'error', message: t('fileReadError') });
      onFileSelectedAction(null);
      setLocalPreviewUrl(null);
    };
    reader.readAsDataURL(file);

    event.target.value = '';
  };

  const handleRemoveClick = () => {
    if (!user) return;

    if (localPreviewUrl) {
      setLocalPreviewUrl(null);
      setValueAction('avatar', '', { shouldDirty: true }); // Always set to '' on removal
      onFileSelectedAction(null);
    } else {
      setValueAction('avatar', '', { shouldDirty: true });
      onFileSelectedAction(null);
    }
    setFormAlertAction(null);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Stack spacing={0} alignItems='center' sx={{ minWidth: '90px' }}>
      <Box
        ref={avatarRef}
        sx={{
          position: 'relative',
          display: 'inline-block',
          borderRadius: '50%',
          overflow: 'hidden',
          mb: 0.5,
        }}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
      >
        <UserAvatar
          displayName={user?.displayName}
          avatar={localPreviewUrl || formAvatarPath} // Prioritize local preview, then form's path
          size={80}
          sx={{
            cursor: 'pointer',
            border: '4px solid var(--mui-palette-background-paper)',
          }}
        />

        {hover && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              cursor: 'pointer',
              zIndex: 1,
            }}
            onClick={handleUploadClick}
          >
            <Stack direction='column' alignItems='center' spacing={0.5}>
              <CameraIcon color='white' weight='bold' size={20} />
              <Typography variant='caption' color='white'>
                {t('upload')}
              </Typography>
            </Stack>
          </Box>
        )}

        <input
          type='file'
          ref={fileInputRef}
          style={{ display: 'none' }}
          accept='.png,.jpg,.jpeg'
          onChange={handleFileChange}
        />
      </Box>{' '}
      {(localPreviewUrl || formAvatarPath) && (
        <Button
          variant='text'
          size='small'
          color='error'
          startIcon={<XIcon size={14} />}
          onClick={handleRemoveClick}
          sx={{
            mt: -0.25,
            mb: 1,
            py: 0,
            minHeight: '24px',
            fontSize: '0.75rem',
            whiteSpace: 'nowrap',
            minWidth: '100px',
          }}
        >
          {localPreviewUrl ? t('cancel') : t('remove')}
        </Button>
      )}
    </Stack>
  );
}
