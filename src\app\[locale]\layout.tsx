import '@/styles/global.css';

import { routing } from '@/i18n/routing';
import InitColorSchemeScript from '@mui/material/InitColorSchemeScript';
import { clsx } from 'clsx';
import type { Viewport } from 'next';
import { hasLocale, Locale, NextIntlClientProvider } from 'next-intl';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { Inter } from 'next/font/google';
import { notFound } from 'next/navigation';
import { ReactNode } from 'react';

import { LocalizationProvider } from '@/components/core/localization-provider';
import { ThemeProvider } from '@/components/core/theme-provider/theme-provider';

export const viewport = {
  width: 'device-width',
  initialScale: 1,
} satisfies Viewport;

type Props = {
  children: ReactNode;
  params: Promise<{ locale: Locale }>;
};

const inter = Inter({ subsets: ['latin'] });

// https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering
export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export async function generateMetadata(props: Omit<Props, 'children'>) {
  const { locale } = await props.params;

  const t = await getTranslations({ locale, namespace: 'common' });

  return {
    title: t('title'),
  };
}

export default async function Layout({ children, params }: Props) {
  // Ensure that the incoming `locale` is valid
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Allow static rendering
  // https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering
  setRequestLocale(locale);

  return (
    <html className='h-full' lang={locale} suppressHydrationWarning>
      <body className={clsx(inter.className, 'flex h-full flex-col')}>
        {/* https://mui.com/material-ui/react-init-color-scheme-script/ for suppressHydrationWarning and InitColorSchemeScript details */}
        <InitColorSchemeScript attribute='class' defaultMode='system' />
        <NextIntlClientProvider>
          <LocalizationProvider>
            <ThemeProvider>{children}</ThemeProvider>
          </LocalizationProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
