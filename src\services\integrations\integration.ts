import { WorkspaceIntegrationWithChannels } from '@/lib/models/integration';
import { IntegrationApiService } from '@/services/api/integration';
import { IntegrationChannel, WorkspaceIntegration } from '@prisma/client';
import * as React from 'react';

export type IntegrationStatus = 'available' | 'coming-soon' | 'installed';

export interface Integration {
  id: IntegrationChannel;
  title: string;
  darkLogo?: boolean;
  status: IntegrationStatus;
  capabilities: string[]; // Capability keys that will be reused across integrations
}

export interface IntegrationConfigProps {
  integration: Integration;
  workspaceId?: string;
  integrationDetails?: WorkspaceIntegration;
  integrationHandler: IntegrationHandler;
  isDocumentationOnly?: boolean;
}

export interface IntegrationInstallProps {
  workspaceId: string;
  integrationApiService: IntegrationApiService;
  setIsLoading: (_isLoading: boolean) => void;
}

export interface IntegrationUninstallProps {
  workspaceId: string;
  integrationApiService: IntegrationApiService;
}

export interface IntegrationConfigComponentProps {
  workspaceId: string;
  workspaceIntegrations?: WorkspaceIntegrationWithChannels[];
  integrationHandler: IntegrationHandler;
  integration: Integration;
}

export interface IntegrationDocComponentProps {
  workspaceId: string;
  workspaceIntegrations?: WorkspaceIntegrationWithChannels[];
  integrationHandler: IntegrationHandler;
  integration: Integration;
}

export interface IntegrationHandler {
  // Basic integration info
  getIntegration: () => Integration;

  // Installation methods
  onInstallAction: (_props: IntegrationInstallProps) => Promise<boolean>;
  onUninstallAction: (_props: IntegrationUninstallProps) => Promise<boolean>;

  // Configuration component - returns a component type, not an instance
  ConfigComponent?: React.ComponentType<IntegrationConfigComponentProps>;

  // Documentation component - returns a component type, not an instance
  DocComponent?: React.ComponentType<IntegrationDocComponentProps>;

  // Optional methods for custom installation flows
  hasCustomInstallFlow: () => boolean;
}
