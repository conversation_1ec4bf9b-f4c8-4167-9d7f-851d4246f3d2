import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import type { SxProps } from '@mui/material/styles';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { DotsThreeVerticalIcon } from '@phosphor-icons/react/dist/ssr/DotsThreeVertical';
import { useTranslations } from 'next-intl';
import React from 'react';

export interface Repository {
  id: string;
  name: string;
  updatedAt: Date;
}

export interface RecentRepositoriesProps {
  products?: Repository[];
  sx?: SxProps;
}

export function LatestProducts({ products = [], sx }: RecentRepositoriesProps): React.JSX.Element {
  const t = useTranslations('git.overview');

  return (
    <Card sx={sx}>
      <CardHeader title={t('titles.recentRepositories')} />
      <Divider />
      <List>
        {products.map((product, index) => (
          <ListItem divider={index < products.length - 1} key={product.id}>
            <ListItemText
              primary={product.name}
              secondary={`${t('git.lastCommit', { date: product.updatedAt })}`}
              slotProps={{
                primary: { variant: 'subtitle1' },
                secondary: { variant: 'body2' },
              }}
            />
            <IconButton edge='end'>
              <DotsThreeVerticalIcon weight='bold' />
            </IconButton>
          </ListItem>
        ))}
      </List>
      <Divider />
      <CardActions sx={{ justifyContent: 'flex-end' }}>
        <Button
          color='inherit'
          endIcon={<ArrowRightIcon fontSize='var(--icon-fontSize-md)' />}
          size='small'
          variant='text'
        >
          {t('actions.viewAll')}
        </Button>
      </CardActions>
    </Card>
  );
}
