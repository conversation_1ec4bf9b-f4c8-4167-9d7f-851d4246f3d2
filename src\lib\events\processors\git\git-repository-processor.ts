import { eventProcessorRegistry } from '@/lib/events/processors/registry';
import { GitRepositoryEvent, GitRepositoryEventAction } from '@/lib/events/types/git';
import { logger } from '@/lib/logger/default-logger';
import { createIntegrationProfile } from '@/lib/profile/integration-profile-creator';
import { db } from '@/services/db';
import { GitRepository, GitRepositoryState, GitRepositoryVisibility, IntegrationChannel } from '@prisma/client';

// Register the repository event processor
eventProcessorRegistry.register(GitRepositoryEvent, async (event) => {
  logger.info(`Processing ${event.id} event for repository ${event.repository.name}...`);

  const workspaceId = event.workspaceId;
  if (!workspaceId) {
    throw new Error('Workspace ID is required for repository event processing');
  }

  const integrationId = event.integrationId;
  if (!integrationId) {
    throw new Error('Integration ID is required for repository event processing');
  }

  try {
    let profileId: string | undefined;

    if (event.author) {
      const profile = await createIntegrationProfile(event.author);

      if (profile) {
        profileId = profile.id;
      }
    }

    // Determine repository state and update data based on event action
    const { state, updateData } = determineRepositoryStateAndData(event, profileId);

    await upsertRepository({ repository: event.repository, workspaceId, updateData, integrationId, state });

    logger.info(`Successfully processed ${event.id} event for repository ${event.repository.name}`);
  } catch (error) {
    // We don't throw the error further to prevent event processing from failing completely
    // When we add the messaging queue we may want to throw the error
    // The event will be considered processed, but the error is logged
    logger.error(`Error processing repository ${event.repository.name} for workspace ${workspaceId}:`, error);
  }
});

export async function upsertRepository(data: {
  workspaceId: string;
  integrationId: string;
  repository: Partial<GitRepository>;
  updateData?: Record<string, any>;
  state?: GitRepositoryState;
}): Promise<GitRepository> {
  if (!data.repository.idOnChannel) {
    throw new Error('Repository ID on channel is required for repository event processing');
  }

  if (!data.repository.createdAt) {
    throw new Error('Repository createdAt field is required for repository event processing');
  }

  if (!data.state) {
    data.state = GitRepositoryState.Active;

    if (data.repository.archivedAt) {
      data.state = GitRepositoryState.Archived;
    } else if (data.repository.deletedAt) {
      data.state = GitRepositoryState.Deleted;
    }
  }

  return await db.gitRepository.upsert({
    where: {
      unique: {
        idOnChannel: data.repository.idOnChannel,
        channel: IntegrationChannel.GitHub,
        workspaceId: data.workspaceId,
      },
    },
    update: {
      ...data.updateData,
      name: data.repository.name,
      defaultBranch: data.repository.defaultBranch,
      visibility: data.repository.visibility as GitRepositoryVisibility,
    },
    create: {
      idOnChannel: data.repository.idOnChannel,
      name: data.repository.name || '',
      workspaceId: data.workspaceId,
      integrationId: data.integrationId,
      channel: IntegrationChannel.GitHub,
      defaultBranch: data.repository.defaultBranch || '',
      visibility: data.repository.visibility as GitRepositoryVisibility,
      state: data.state,
      createdAt: data.repository.createdAt,
      deletedAt: data.updateData?.deletedAt || undefined,
      archivedAt: data.updateData?.archivedAt || undefined,
      createdById: data.updateData?.createdById || undefined,
      deletedById: data.updateData?.deletedById || undefined,
      archivedById: data.updateData?.archivedById || undefined,
    },
  });
}

/**
 * Helper function to determine repository state and update data based on event action
 * This makes the code more maintainable and testable
 */
function determineRepositoryStateAndData(
  event: GitRepositoryEvent,
  profileId?: string
): {
  state: GitRepositoryState;
  updateData: Record<string, any>;
} {
  let state: GitRepositoryState = GitRepositoryState.Active;
  const updateData: Record<string, any> = {};

  if (event.repositoryEventAction === GitRepositoryEventAction.Created) {
    state = GitRepositoryState.Active;
    updateData.createdAt = event.timestamp;
    updateData.createdById = profileId;
  } else if (event.repositoryEventAction === GitRepositoryEventAction.Deleted) {
    state = GitRepositoryState.Deleted;
    updateData.deletedAt = event.timestamp;
    updateData.deletedById = profileId;
  } else if (event.repositoryEventAction === GitRepositoryEventAction.Archived) {
    state = GitRepositoryState.Archived;
    updateData.archivedAt = event.timestamp;
    updateData.archivedById = profileId;
  } else if (event.repositoryEventAction === GitRepositoryEventAction.Unarchived) {
    state = GitRepositoryState.Active;
    updateData.archivedAt = null;
    updateData.archivedById = '';
  }

  if (event.repository.createdAt) {
    updateData.createdAt = event.repository.createdAt;
  }

  updateData.state = state;

  return { state, updateData };
}
