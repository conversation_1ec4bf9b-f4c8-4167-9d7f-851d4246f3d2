'use client';

import Avatar, { type AvatarProps } from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import React from 'react';

interface UserAvatarProps extends AvatarProps {
  displayName: string | null | undefined;
  avatar: string | null | undefined;
  bgcolor?: string;
  size?: number;
}

function getInitials(name: string | null | undefined): string {
  if (!name) {
    return '';
  }

  return name
    .split(' ')
    .map((n) => n[0])
    .slice(0, 2)
    .join('')
    .toUpperCase();
}

/**
 * A reusable component to display either a user's image avatar or their initials if no image is available.
 *
 * @param {UserAvatarProps} props
 */
export function UserAvatar({
  displayName,
  avatar,
  size = 40,
  bgcolor = 'primary.main',
  sx,
  ...otherProps
}: UserAvatarProps): React.ReactElement {
  if (avatar) {
    return <Avatar src={avatar} sx={{ width: size, height: size, ...sx }} {...otherProps} />;
  }

  const initials = getInitials(displayName);

  return (
    <Avatar sx={{ width: size, bgcolor: bgcolor, height: size, ...sx }} {...otherProps}>
      {initials && (
        <Typography variant='subtitle1' color='inherit'>
          {initials}
        </Typography>
      )}
    </Avatar>
  );
}
