'use client';

import { useRouter } from '@/i18n/navigation';
import { integrationsRegistry } from '@/services/integrations';
import Box from '@mui/material/Box';
import Pagination from '@mui/material/Pagination';
import Skeleton from '@mui/material/Skeleton';
import Stack from '@mui/material/Stack';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Typography from '@mui/material/Typography';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { ErrorState } from '@/components/settings/integrations/error-state';
import { IntegrationsFilters } from '@/components/settings/integrations/integrations-filters';
import { IntegrationsList } from '@/components/settings/integrations/integrations-list';
import { NoResults } from '@/components/settings/integrations/no-results';
import { ViewMode } from '@/components/settings/integrations/view-toggle';
import { useWorkspace } from '@/contexts/workspace-context';
import { useApiServices } from '@/hooks/use-api-services';
import { paths } from '@/paths';

import { CombinedIntegration } from '@/lib/models/integration';
import { Alert, AlertTitle } from '@mui/material';
import { IntegrationStatus } from '@prisma/client';

export function IntegrationsPageClient(): React.JSX.Element {
  const t = useTranslations('settings.integrations');
  const router = useRouter();
  const [searchQuery, setSearchQuery] = React.useState('');
  const [viewMode, setViewMode] = React.useState<ViewMode>('card');
  const [currentTab, setCurrentTab] = React.useState('all');
  const [isLoading, setIsLoading] = React.useState(true);
  const [hasError, setHasError] = React.useState(false);
  const [hasIntegrationInstallError, setIntegrationInstallError] = React.useState(false);
  // Add a state to track if initial data has been loaded
  const [isInitialDataLoaded, setIsInitialDataLoaded] = React.useState(false);
  // Add a state to track if search/filter has been applied
  const [searchComplete, setSearchComplete] = React.useState(false);

  const { integrationApiService } = useApiServices();
  const { currentWorkspace } = useWorkspace();

  const [allIntegrations, setAllIntegrations] = React.useState<CombinedIntegration[]>([]);
  const [displayedIntegrations, setDisplayedIntegrations] = React.useState<CombinedIntegration[]>([]);

  React.useEffect(() => {
    async function loadIntegrations() {
      if (!currentWorkspace) return;
      setIsLoading(true);
      setHasError(false);
      setSearchComplete(false);

      const availableIntegrations = integrationsRegistry.getAllIntegrations();
      try {
        const existingIntegrations = await integrationApiService.get(currentWorkspace.id);

        const combined = availableIntegrations.map((regIntegration) => {
          const channelIntegrations = existingIntegrations.filter(
            (wsIntegration) => wsIntegration.channel === regIntegration.id
          );

          const isInstalled = channelIntegrations.some((integration) => {
            return integration.status !== IntegrationStatus.Uninstalled;
          });

          return {
            ...regIntegration,
            installed: isInstalled,
            integrations: channelIntegrations,
          };
        });

        setAllIntegrations(combined);
        setHasError(false);
        // Mark initial data as loaded
        setIsInitialDataLoaded(true);
      } catch (error) {
        console.error('Failed to load workspace integrations:', error);
        setHasError(true);
        // Show registry integrations but mark them as not installed due to error
        const combined = availableIntegrations.map((regIntegration) => ({
          ...regIntegration,
          installed: false,
        }));
        setAllIntegrations(combined);
        // Even with an error, mark data as loaded since we have fallback data
        setIsInitialDataLoaded(true);
      } finally {
        setIsLoading(false);
      }
    }

    loadIntegrations();
  }, [currentWorkspace, integrationApiService]);

  const handleRefresh = React.useCallback(async () => {
    if (!currentWorkspace) return;
    setIsLoading(true);
    setHasError(false);
    setSearchComplete(false);

    const registryIntegrations = integrationsRegistry.getAllIntegrations();
    try {
      const workspaceIntegrationsFromApi = await integrationApiService.get(currentWorkspace.id);

      const combined = registryIntegrations.map((regIntegration) => {
        const installedIntegration = workspaceIntegrationsFromApi.find(
          (wsIntegration) => wsIntegration.channel === regIntegration.id
        );

        return {
          ...regIntegration,
          installed: !!installedIntegration,
          installedData: installedIntegration,
        };
      });

      setAllIntegrations(combined);
      setHasError(false);
      setIsInitialDataLoaded(true);
    } catch (error) {
      console.error('Failed to refresh workspace integrations:', error);
      setHasError(true);
    } finally {
      setIsLoading(false);
    }
  }, [currentWorkspace, integrationApiService]);

  React.useEffect(() => {
    let filtered = allIntegrations;

    if (searchQuery) {
      const lowerCaseQuery = searchQuery.toLowerCase();
      filtered = filtered.filter((integration: CombinedIntegration) => {
        const description = t(`descriptions.${integration.id.toLowerCase()}` as any);
        return (
          integration.title.toLowerCase().includes(lowerCaseQuery) ||
          description.toLowerCase().includes(lowerCaseQuery) ||
          integration.capabilities.some((capability: string) => {
            const capabilityText = t(`capabilities.${capability}` as any);
            return capabilityText.toLowerCase().includes(lowerCaseQuery);
          })
        );
      });
    }

    if (currentTab === 'installed') {
      filtered = filtered.filter((integration) => integration.installed);
    } else if (currentTab === 'available') {
      filtered = filtered.filter((integration) => !integration.installed);
    }

    setDisplayedIntegrations(filtered);

    // Only set searchComplete to true when we have loaded initial data
    if (isInitialDataLoaded) {
      setSearchComplete(true);
    }
  }, [searchQuery, t, allIntegrations, currentTab, isInitialDataLoaded]);

  const handleInstall = async (integration: CombinedIntegration) => {
    if (!currentWorkspace) {
      console.error('No workspace selected');
      return false;
    }

    const handler = integrationsRegistry.getHandler(integration.id);

    if (!handler) {
      return false;
    }

    if (handler.hasCustomInstallFlow()) {
      router.push({ pathname: paths.settings.integrations.details, query: { id: integration.id } });

      return true;
    }

    try {
      await handler.onInstallAction({
        workspaceId: currentWorkspace.id,
        integrationApiService,
        setIsLoading, // Pass setIsLoading to the handler
      });
    } catch (error) {
      console.error(`Failed to install ${integration.id}:`, error);

      setIntegrationInstallError(true);
      setIsLoading(false);

      return false;
    }

    return true;
  };

  const handleUninstall = async (integration: CombinedIntegration) => {
    if (!currentWorkspace) {
      console.error('No workspace selected');
      return false;
    }

    const handler = integrationsRegistry.getHandler(integration.id);

    if (!handler) {
      return false;
    }

    try {
      setIsLoading(true);
      setSearchComplete(false);
      await handler.onUninstallAction({
        workspaceId: currentWorkspace.id,
        integrationApiService,
      });

      setAllIntegrations((prevIntegrations) =>
        prevIntegrations.map((prevInt) =>
          prevInt.id === integration.id ? { ...prevInt, installed: false, installedData: undefined } : prevInt
        )
      );
    } catch (error) {
      console.error(`Failed to uninstall ${integration.id}:`, error);
      // Optionally, show a notification to the user

      return false;
    } finally {
      setIsLoading(false);
    }

    return true;
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    setCurrentTab(newValue);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
  };

  // Calculate counts for tabs based on search results
  const getFilteredIntegrations = () => {
    let filtered = allIntegrations;

    if (searchQuery) {
      const lowerCaseQuery = searchQuery.toLowerCase();
      filtered = filtered.filter((integration: CombinedIntegration) => {
        const description = t(`descriptions.${integration.id.toLowerCase()}` as any);
        return (
          integration.title.toLowerCase().includes(lowerCaseQuery) ||
          description.toLowerCase().includes(lowerCaseQuery) ||
          integration.capabilities.some((capability: string) => {
            const capabilityText = t(`capabilities.${capability}` as any);
            return capabilityText.toLowerCase().includes(lowerCaseQuery);
          })
        );
      });
    }

    return filtered;
  };

  const searchFilteredIntegrations = getFilteredIntegrations();
  // When there's an error, show 0 counts since we can't reliably determine installation status
  const allCount = hasError ? 0 : searchFilteredIntegrations.length;
  const installedCount = hasError
    ? 0
    : searchFilteredIntegrations.filter((integration) => integration.installed).length;
  const availableCount = hasError
    ? 0
    : searchFilteredIntegrations.filter((integration) => !integration.installed).length;

  // Use the current tab's count for skeletons, fallback to 6 if 0
  let skeletonCount = 6;
  if (currentTab === 'all' && allCount > 0) skeletonCount = allCount;
  else if (currentTab === 'installed' && installedCount > 0) skeletonCount = installedCount;
  else if (currentTab === 'available' && availableCount > 0) skeletonCount = availableCount;

  // Initial loading skeleton for title, filters, tabs, and list
  if (isLoading && !isInitialDataLoaded) {
    return (
      <Stack spacing={3}>
        {/* Title placeholder */}
        <Skeleton variant='text' width='30%' height={40} />
        {/* Filters placeholders */}
        <Stack direction='row' spacing={2} alignItems='center'>
          <Skeleton variant='rectangular' width={200} height={48} />
          <Skeleton variant='rectangular' width={200} height={48} />
          <Skeleton variant='rectangular' width={100} height={48} />
        </Stack>
        {/* Tabs placeholder */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Skeleton variant='rectangular' height={48} />
        </Box>
        {/* Integrations list skeleton with dynamic card count */}
        <IntegrationsList integrations={[]} viewMode={viewMode} isLoading skeletonCount={skeletonCount} />
      </Stack>
    );
  }

  return (
    <Stack spacing={3}>
      <Typography variant='h4'>{t('title')}</Typography>

      <IntegrationsFilters
        searchQuery={searchQuery}
        onSearchChangeAction={setSearchQuery}
        viewMode={viewMode}
        onViewModeChangeAction={setViewMode}
        onRefreshAction={handleRefresh}
        isRefreshing={isLoading}
      />

      {hasIntegrationInstallError && !isLoading && (
        <Alert
          severity='error'
          onClose={() => {
            setIntegrationInstallError(false);
          }}
        >
          <AlertTitle>{t('error.install.title')}</AlertTitle>
          {t('error.install.description')}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={currentTab} onChange={handleTabChange} aria-label='integration tabs'>
          <Tab label={`${t('tabs.all' as any)} (${allCount})`} value='all' />
          <Tab label={`${t('tabs.installed' as any)} (${installedCount})`} value='installed' />
          <Tab label={`${t('tabs.available' as any)} (${availableCount})`} value='available' />
        </Tabs>
      </Box>

      {/* List with skeletons on loading, errors and no-results when not loading */}
      {hasError && !isLoading ? (
        <ErrorState onRetryAction={handleRefresh} />
      ) : searchComplete && displayedIntegrations.length === 0 && !isLoading ? (
        <NoResults onClearSearchAction={handleClearSearch} />
      ) : (
        <IntegrationsList
          integrations={displayedIntegrations}
          viewMode={viewMode}
          isLoading={isLoading}
          onInstallAction={handleInstall}
          onUninstallAction={handleUninstall}
          skeletonCount={skeletonCount}
        />
      )}

      {displayedIntegrations.length > 0 && !isLoading && searchComplete && (
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Pagination count={1} size='small' />
        </Box>
      )}
    </Stack>
  );
}
