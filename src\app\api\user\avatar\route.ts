import { adminStorage } from '@/services/firebase/admin';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';
import { randomUUID } from 'crypto';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /api/user/avatar:
 *   post:
 *     security:
 *       - BearerAuth: []
 *     tags:
 *       - User
 *     description: Returns a presigned URL for uploading a user avatar
 *     parameters:
 *       - in: query
 *         name: extension
 *         schema:
 *           type: string
 *           enum: [jpg, jpeg, png]
 *         required: true
 *         description: The file extension of the avatar (jpg, jpeg, or png)
 *       - in: query
 *         name: content-length
 *         schema:
 *           type: number
 *         required: true
 *         description: The size of the avatar in bytes
 *     responses:
 *       200:
 *         description: Presigned URL for uploading the avatar
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 url:
 *                   type: string
 *                   description: The presigned URL for uploading the avatar
 *                 expiration:
 *                   type: string
 *                   format: date-time
 *                   description: The expiration date and time of the presigned URL
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid extension parameter
 *       500:
 *         description: Failed to generate signed URL
 */
export async function POST(request: NextRequest) {
  // Get the authenticated user from Firebase
  const authResult = await getAuthenticatedAppForUser();

  // If no user is authenticated, return 401
  if (!authResult?.currentUser) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const contentType = request.nextUrl.searchParams.get('content-type');
  if (!contentType) {
    return NextResponse.json({ error: 'Invalid content-type parameter' }, { status: 400 });
  }

  const extension = contentType.includes('/') ? contentType.split('/')[1] : contentType;

  const contentLength = parseInt(request.nextUrl.searchParams.get('content-length') || '0');

  if (!contentLength || contentLength <= 0) {
    return NextResponse.json({ error: 'Invalid content-length parameter' }, { status: 400 });
  }

  if (contentLength > 2 * 1024 * 1024) {
    return NextResponse.json({ error: 'Avatar is too big' }, { status: 400 });
  }

  try {
    const path = `user-avatars/${authResult.currentUser.uid}/${randomUUID()}.${extension}`;

    const bucket = adminStorage.bucket();
    const file = bucket.file(path);

    // 10 minutes from now
    const expires = new Date(Date.now() + 10 * 60 * 1000);

    const cacheControl = 'private,max-age=172800';

    const [url] = await file.getSignedUrl({
      version: 'v4',
      action: 'write',
      contentType: contentType,
      extensionHeaders: {
        'Cache-Control': cacheControl,
      },
      expires: expires,
    });

    return NextResponse.json({ url, expiration: expires, path: path, cacheControl });
  } catch (error) {
    console.error('Error generating signed URL:', error);

    return NextResponse.json({ error: 'Failed to generate signed URL' }, { status: 500 });
  }
}
