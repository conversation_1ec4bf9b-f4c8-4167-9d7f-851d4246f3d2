'use client';

import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormHelperText from '@mui/material/FormHelperText';
import InputLabel from '@mui/material/InputLabel';
import Link from '@mui/material/Link';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useTranslations } from 'next-intl';
import NextLink from 'next/link';
import * as React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import { useAuth } from '@/contexts/firebase-auth-context';
import { logger } from '@/lib/logger/default-logger';
import { getPathWithReturnTo, getReturnToParam } from '@/lib/return-to-utils';
import { paths } from '@/paths';
import { useSearchParams } from 'next/navigation';

type Values = {
  displayName: string;
  email: string;
  password: string;
  terms: boolean;
};

const defaultValues = {
  displayName: '',
  email: '',
  password: '',
  terms: false,
} satisfies Values;

export function SignUpForm(): React.JSX.Element {
  const router = useRouter();
  const params = useSearchParams();
  const { signUpWithEmail, clearError, error: authError } = useAuth();
  const t = useTranslations('auth');

  // Get returnTo parameter for preserving across navigation
  const returnTo = getReturnToParam(params);

  // Create schema with translations
  const schema = React.useMemo(() => {
    return zod.object({
      displayName: zod.string().min(1, { message: t('signUp.displayNameRequired') }),
      email: zod
        .string()
        .min(1, { message: t('signUp.emailRequired') })
        .email(),
      password: zod.string().min(6, { message: t('signUp.passwordRequired') }),
      terms: zod.boolean().refine((value) => value, t('signUp.termsRequired')),
    });
  }, [t]);

  const [isPending, setIsPending] = React.useState<boolean>(false);
  const [errorDismissed, setErrorDismissed] = React.useState<boolean>(false);
  const [formErrorDismissed, setFormErrorDismissed] = React.useState<boolean>(false);

  const {
    control,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<Values>({ defaultValues, resolver: zodResolver(schema) });

  const onSubmit = React.useCallback(
    async (values: Values): Promise<void> => {
      setIsPending(true);
      // Reset error dismissed states on new submission
      setErrorDismissed(false);
      setFormErrorDismissed(false);

      try {
        // Sign up with Firebase
        await signUpWithEmail(values.email, values.password);

        // After successful signup, redirect to returnTo or home
        if (returnTo) {
          router.replace(returnTo as any);
          return;
        }

        router.push(paths.home);
      } catch (error) {
        // Parse Firebase error message and provide a user-friendly message
        let errorMessage = t('errors.registrationFailed');

        // Only in development, log the actual error
        if (process.env.NODE_ENV === 'development') {
          logger.warn('Sign up error:', error);
        }

        // Handle specific error codes
        const firebaseError = error as { code?: string; message: string };
        if (firebaseError.code === 'auth/email-already-in-use') {
          errorMessage = t('errors.emailInUse');
        } else if (firebaseError.code === 'auth/weak-password') {
          errorMessage = t('errors.weakPassword');
        } else if (firebaseError.code === 'auth/invalid-email') {
          errorMessage = t('errors.registrationFailed');
        } else if (firebaseError.code === 'auth/operation-not-allowed') {
          errorMessage = t('errors.registrationFailed');
        }

        setError('root', { type: 'server', message: errorMessage });
        setIsPending(false);
      }
    },
    [signUpWithEmail, router, setError, setErrorDismissed, setFormErrorDismissed, t, returnTo]
  );

  return (
    <Stack spacing={3}>
      <Stack spacing={1}>
        <Typography variant='h4'>{t('signUp.title')}</Typography>
        <Typography color='text.secondary' variant='body2'>
          {t('signUp.haveAccount')}{' '}
          <Link
            component={NextLink}
            href={getPathWithReturnTo(paths.auth.signIn, returnTo)}
            underline='hover'
            variant='subtitle2'
          >
            {t('signUp.signInLink')}
          </Link>
        </Typography>
      </Stack>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={2}>
          <Controller
            control={control}
            name='displayName'
            render={({ field }) => (
              <FormControl error={Boolean(errors.displayName)}>
                <InputLabel>{t('signUp.displayNameLabel')}</InputLabel>
                <OutlinedInput {...field} label={t('signUp.displayNameLabel')} />
                {errors.displayName ? <FormHelperText>{errors.displayName.message}</FormHelperText> : null}
              </FormControl>
            )}
          />
          <Controller
            control={control}
            name='email'
            render={({ field }) => (
              <FormControl error={Boolean(errors.email)}>
                <InputLabel>{t('signUp.emailLabel')}</InputLabel>
                <OutlinedInput {...field} label={t('signUp.emailLabel')} type='email' />
                {errors.email ? <FormHelperText>{errors.email.message}</FormHelperText> : null}
              </FormControl>
            )}
          />
          <Controller
            control={control}
            name='password'
            render={({ field }) => (
              <FormControl error={Boolean(errors.password)}>
                <InputLabel>{t('signUp.passwordLabel')}</InputLabel>
                <OutlinedInput {...field} label={t('signUp.passwordLabel')} type='password' />
                {errors.password ? <FormHelperText>{errors.password.message}</FormHelperText> : null}
              </FormControl>
            )}
          />
          <Controller
            control={control}
            name='terms'
            render={({ field }) => (
              <div>
                <FormControlLabel
                  control={<Checkbox {...field} />}
                  label={
                    <React.Fragment>
                      I have read the <Link>terms and conditions</Link>
                    </React.Fragment>
                  }
                />
                {errors.terms ? <FormHelperText error>{errors.terms.message}</FormHelperText> : null}
              </div>
            )}
          />
          {/* Display form validation errors */}
          {errors.root && !formErrorDismissed ? (
            <Alert
              color='error'
              severity='error'
              sx={{ mb: 2 }}
              onClose={() => {
                // We can't directly clear form errors from react-hook-form
                // But we can mark the error as dismissed so it doesn't show
                setFormErrorDismissed(true);
                clearError();
              }}
            >
              {errors.root.message}
            </Alert>
          ) : null}

          {/* Display Firebase authentication errors */}
          {authError && !errors.root && !errorDismissed ? (
            <Alert
              color='error'
              severity='error'
              sx={{ mb: 2 }}
              onClose={() => {
                clearError();
                setErrorDismissed(true);
              }}
            >
              {authError.includes('auth/email-already-in-use')
                ? t('errors.emailInUse')
                : authError.includes('auth/weak-password')
                  ? t('errors.weakPassword')
                  : authError.includes('auth/')
                    ? t('errors.registrationFailed')
                    : t('errors.registrationFailed')}
            </Alert>
          ) : null}
          <Button disabled={isPending} type='submit' variant='contained'>
            {t('signUp.submitButton')}
          </Button>
        </Stack>
      </form>
    </Stack>
  );
}
