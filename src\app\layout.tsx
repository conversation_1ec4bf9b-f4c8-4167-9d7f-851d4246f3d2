import { User } from 'firebase/auth';
import { ReactNode } from 'react';

import { AuthProvider } from '@/contexts/firebase-auth-context';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

// Force next.js to treat this route as server-side rendered
export const dynamic = 'force-dynamic';

export default async function RootLayout({ children }: { children: ReactNode }) {
  const result = await getAuthenticatedAppForUser();
  const currentUser = result?.currentUser;

  // Convert to JSON to avoid type issues
  const serializedUser = currentUser ? (currentUser.toJSON() as User) : null;

  return <AuthProvider initialUser={serializedUser}>{children}</AuthProvider>;
}
