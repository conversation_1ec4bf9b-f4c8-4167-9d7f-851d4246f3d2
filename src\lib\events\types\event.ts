import { IntegrationChannel, IntegrationProfile } from '@prisma/client';

// eslint-disable no-unused-vars
export enum EventType {
  Git = 'git',
}
// eslint-enable no-unused-vars

// Base git event interface
export interface Event {
  // Event global unique ID
  id: string;

  // The type of the event
  type: EventType;

  timestamp: Date;

  channel: IntegrationChannel;

  // Author of the event itself.
  // May be null if the event is not associated with a user.
  author?: Partial<IntegrationProfile>;

  // If this event is related to an integration then this will be the integration ID
  integrationId?: String;

  // If this event is related to a workspace then this will be the workspace ID
  workspaceId?: String;

  rawPayload?: Record<string, any>;
}
