import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import * as React from 'react';

import { ProcessEfficiency } from '@/components/insights/process-efficiency';
import { config } from '@/config';

export const metadata = {
  title: `Process Efficiency | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const t = await getTranslations('insights');

  return (
    <Stack spacing={3}>
      <Typography variant='h4'>{t('categories.processEfficiency')}</Typography>
      <Typography color='text.secondary' variant='body1'>
        Analyzing workflow efficiency and identifying opportunities for process improvements.
      </Typography>

      <ProcessEfficiency />
    </Stack>
  );
}
